import request from '@/utils/request'

// 查询WMS任务表列表
export function listWmsTask(query) {
  return request({
    url: '/task/wmsTask/list',
    method: 'get',
    params: query
  })
}

// 查询WMS任务表详细
export function getWmsTask(wmsTaskId) {
  return request({
    url: '/task/wmsTask/' + wmsTaskId,
    method: 'get'
  })
}

// 新增WMS任务表
export function addWmsTask(data) {
  return request({
    url: '/task/wmsTask',
    method: 'post',
    data: data
  })
}

// 修改WMS任务表
export function updateWmsTask(data) {
  return request({
    url: '/task/wmsTask',
    method: 'put',
    data: data
  })
}

// 删除WMS任务表
export function delWmsTask(wmsTaskId) {
  return request({
    url: '/task/wmsTask/' + wmsTaskId,
    method: 'delete'
  })
}
