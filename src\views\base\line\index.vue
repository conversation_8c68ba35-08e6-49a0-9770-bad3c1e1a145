<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="线路编号" prop="lineCode">
        <el-input
          v-model="queryParams.lineCode"
          placeholder="请输入线路编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="线路类型" prop="lineType">
        <el-select v-model="queryParams.lineType" placeholder="请选择线路类型" clearable>
          <el-option
            v-for="dict in dict.type.line_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="起始位置" prop="startPositionId">
        <el-input
          v-model="queryParams.startPositionId"
          placeholder="请输入起始位置ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="结束位置" prop="endPositionId">
        <el-input
          v-model="queryParams.endPositionId"
          placeholder="请输入结束位置ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini"
          @click="handleAdd"
          v-hasPermi="['base:line:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['base:line:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['base:line:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini"
          @click="handleExport"
          v-hasPermi="['base:line:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="lineList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="线路ID" align="center" prop="lineId" />
      <el-table-column label="线路编号" align="center" prop="lineCode" />
      <el-table-column label="线路名称" align="center" prop="lineName" />
      <el-table-column label="线路类型" align="center" prop="lineType">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.line_type" :value="scope.row.lineType" />
        </template>
      </el-table-column>
      <el-table-column label="起始位置" align="center" prop="startPositionId" />
      <el-table-column label="结束位置" align="center" prop="endPositionId" />
      <el-table-column label="线路长度" align="center" prop="length" />
      <el-table-column label="速度限制" align="center" prop="speedLimit" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['base:line:edit']"
          >修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['base:line:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改线路表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="线路编号" prop="lineCode">
          <el-input v-model="form.lineCode" placeholder="请输入线路编号" />
        </el-form-item>
        <el-form-item label="线路名称" prop="lineName">
          <el-input v-model="form.lineName" placeholder="请输入线路名称" />
        </el-form-item>
        <el-form-item label="线路类型" prop="lineType">
          <el-select v-model="form.lineType" placeholder="请选择线路类型">
            <el-option
              v-for="dict in dict.type.line_type"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="起始位置" prop="startPositionId">
          <el-input v-model="form.startPositionId" placeholder="请输入起始位置ID" />
        </el-form-item>
        <el-form-item label="结束位置" prop="endPositionId">
          <el-input v-model="form.endPositionId" placeholder="请输入结束位置ID" />
        </el-form-item>
        <el-form-item label="线路长度" prop="length">
          <el-input v-model="form.length" placeholder="请输入线路长度" />
        </el-form-item>
        <el-form-item label="速度限制" prop="speedLimit">
          <el-input v-model="form.speedLimit" placeholder="请输入速度限制" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLine, getLine, delLine, addLine, updateLine } from "@/api/base/line";

export default {
  name: "Line",
  dicts: ['line_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 线路表表格数据
      lineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        lineCode: null,
        lineType: null,
        startPositionId: null,
        endPositionId: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        lineCode: [
          { required: true, message: "线路编号不能为空", trigger: "blur" }
        ],
        lineType: [
          { required: true, message: "线路类型不能为空", trigger: "change" }
        ],
        startPositionId: [
          { required: true, message: "起始位置ID不能为空", trigger: "blur" }
        ],
        endPositionId: [
          { required: true, message: "结束位置ID不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询线路表列表 */
    getList() {
      this.loading = true;
      listLine(this.queryParams).then(response => {
        this.lineList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        lineId: null,
        lineCode: null,
        lineName: null,
        lineType: null,
        startPositionId: null,
        endPositionId: null,
        length: null,
        speedLimit: null,
        delFlag: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.lineId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加线路表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const lineId = row.lineId || this.ids;
      getLine(lineId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改线路表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.lineId != null) {
            updateLine(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLine(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const lineIds = row.lineId || this.ids;
      this.$modal.confirm('是否确认删除@Model.FunctionName)编号为"' + lineIds + '"的数据项？').then(function() {
        return delLine(lineIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('base/line/export', {
        ...this.queryParams
      }, `line_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
