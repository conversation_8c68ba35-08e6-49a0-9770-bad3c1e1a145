import request from '@/utils/request'

// 查询提升机任务列表
export function listHoist(query) {
  return request({
    url: '/system/hoist/list',
    method: 'get',
    params: query
  })
}

// 查询提升机任务详细
export function getHoist(id) {
  return request({
    url: '/system/hoist/' + id,
    method: 'get'
  })
}

// 新增提升机任务
export function addHoist(data) {
  return request({
    url: '/system/hoist',
    method: 'post',
    data: data
  })
}

// 修改提升机任务
export function updateHoist(data) {
  return request({
    url: '/system/hoist',
    method: 'put',
    data: data
  })
}

// 删除提升机任务
export function delHoist(id) {
  return request({
    url: '/system/hoist/' + id,
    method: 'delete'
  })
}
