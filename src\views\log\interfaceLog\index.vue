<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="接口编号" prop="interfaceCode">
        <el-input
          v-model="queryParams.interfaceCode"
          placeholder="请输入接口编号"
          clearable
          style="width: 240px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="接口类型" prop="interfaceType">
        <el-select 
          v-model="queryParams.interfaceType" 
          placeholder="请选择接口类型" 
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.interface_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="业务类型" prop="businessType">
        <el-select 
          v-model="queryParams.businessType" 
          placeholder="请选择业务类型" 
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.business_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="状态" prop="interfaceStatus">
        <el-select 
          v-model="queryParams.interfaceStatus" 
          placeholder="接口状态" 
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.interface_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="操作时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['log:interfaceLog:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleClean"
          v-hasPermi="['log:interfaceLog:remove']"
        >清空</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['log:interfaceLog:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="tables" v-loading="loading" :data="list" @selection-change="handleSelectionChange" :default-sort="defaultSort" @sort-change="handleSortChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="日志编号" align="center" prop="id" />
      <el-table-column label="接口编号" align="center" prop="interfaceCode" :show-overflow-tooltip="true" />
      <el-table-column label="接口名称" align="center" prop="interfaceName" :show-overflow-tooltip="true" />
      <el-table-column label="接口类型" align="center" prop="interfaceType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.interface_type" :value="scope.row.interfaceType"/>
        </template>
      </el-table-column>
      <el-table-column label="业务类型" align="center" prop="businessType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.business_type" :value="scope.row.businessType"/>
        </template>
      </el-table-column>
      <el-table-column label="关联业务单号" align="center" prop="businessNo" :show-overflow-tooltip="true" />
      <el-table-column label="请求地址" align="center" prop="requestUrl" :show-overflow-tooltip="true" />
      <el-table-column label="请求方法" align="center" prop="requestMethod" width="100" :show-overflow-tooltip="true" />
      <el-table-column label="接口状态" align="center" prop="interfaceStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.interface_status" :value="scope.row.interfaceStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="操作时间" align="center" prop="requestTime" width="180" sortable="custom" :sort-orders="['descending', 'ascending']">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.requestTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="消耗时间" align="center" prop="processTime" width="110" :show-overflow-tooltip="true" sortable="custom" :sort-orders="['descending', 'ascending']">
        <template slot-scope="scope">
          <span>{{ scope.row.processTime }}毫秒</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row,scope.index)"
            v-hasPermi="['log:interfaceLog:query']"
          >详细</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 接口日志详细 -->
    <el-dialog title="接口日志详细" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px" size="mini">
        <el-row>
          <el-col :span="12">
            <el-form-item label="接口信息：">{{ form.interfaceCode }} / {{ form.interfaceName }}</el-form-item>
            <el-form-item label="业务信息：">{{ form.businessNo }} / {{ businessTypeFormat(form) }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请求地址：">{{ form.requestUrl }}</el-form-item>
            <el-form-item label="请求方式：">{{ form.requestMethod }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="请求参数：">{{ form.requestParams }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="返回参数：">{{ form.responseData }}</el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="接口状态：">
              <dict-tag :options="dict.type.interface_status" :value="form.interfaceStatus"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="消耗时间：">{{ form.processTime }}毫秒</el-form-item>
          </el-col>
          <el-col :span="10">
            <el-form-item label="操作时间：">{{ parseTime(form.requestTime) }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="错误信息：" v-if="form.interfaceStatus === '1'">{{ form.errorMsg }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listInterfaceLog, delInterfaceLog, cleanInterfaceLog } from "@/api/log/interfaceLog";

export default {
  name: "InterfaceLog",
  dicts: ['interface_type', 'business_type', 'interface_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 默认排序
      defaultSort: {prop: 'requestTime', order: 'descending'},
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        interfaceCode: undefined,
        interfaceType: undefined,
        businessType: undefined,
        interfaceStatus: undefined
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询接口日志列表 */
    getList() {
      this.loading = true;
      listInterfaceLog(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.list = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    // 接口业务类型字典翻译
    businessTypeFormat(row, column) {
      return this.selectDictLabel(this.dict.type.business_type, row.businessType);
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.pageNum = 1;
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    /** 排序触发事件 */
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true;
      this.form = row;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除日志编号为"' + ids + '"的数据项？').then(function() {
        return delInterfaceLog(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 清空按钮操作 */
    handleClean() {
      this.$modal.confirm('是否确认清空所有接口日志数据项？').then(function() {
        return cleanInterfaceLog();
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("清空成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('log/interfaceLog/export', {
        ...this.queryParams
      }, `WMS接口通讯日志_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
