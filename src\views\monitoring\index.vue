<template>
  <div class="app-container" @click="None">
    <!-- 添加报警信息展示栏 -->
    <div class="alarm-bar" v-if="errorList.length > 0">
      <div class="alarm-header">
        <i class="el-icon-warning"></i>
        <span>实时报警信息</span>
      </div>
      <div class="alarm-list">
        <div v-for="item in activeAlarms" :key="item.id"
             :class="['alarm-item', item.errorLevel === 'error' ? 'error-alarm' : 'warning-alarm']">
          <span class="alarm-time">{{ item.errorTime }}</span>
          <span class="alarm-type">{{ item.errorType }}</span>
          <span class="alarm-location">位置: {{ item.errorLocation }}</span>
          <span class="alarm-vehicle">车辆: {{ item.vehicleCode }}</span>
          <el-button
            size="mini"
            type="primary"
            @click="handleProcessError(item)"
            v-if="item.status === '未处理'">处理</el-button>
        </div>
      </div>
    </div>
    <el-form
      :model="queryParams"
      ref="queryForm"
      :inline="true"
      label-width="68px"
    >
      <el-form-item label="方格长宽" prop="GRID_WIDTH">
        <el-input-number
          v-model="queryParams.GRID_WIDTH"
          placeholder="请输入方格长宽"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item label="间隙边长" prop="GAP_WIDTH">
        <el-input-number
          v-model="queryParams.GAP_WIDTH"
          placeholder="请输入间隙"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item label="行数" prop="ROW">
        <el-input-number
          v-model="queryParams.ROW"
          placeholder="请输入行数"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item label="列数" prop="ROC">
        <el-input-number
          v-model="queryParams.ROC"
          placeholder="请输入列数"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item label="楼层数" prop="ceng">
        <el-input-number
          v-model="queryParams.ceng"
          placeholder="请输入楼层数"
          clearable
          size="small"
        />
      </el-form-item>
      <el-form-item>
        <el-button
          type="primary"
          icon="el-icon-search"
          size="mini"
          @click="toCanvas"
        >切换重绘</el-button
        >
      </el-form-item>
    </el-form>
    <div class="flex">
      <div class="left">
        <el-tabs v-model="activeName" @tab-click="handleClick">
          <el-tab-pane label="设备信息" name="1">
            <div class="operation-bar">
              <el-button
                type="primary"
                size="mini"
                @click="handleReset"
                :disabled="!selectedVehicle">复位</el-button>
              <el-button
                type="success"
                size="mini"
                @click="handleEnable"
                :disabled="!selectedVehicle">启用</el-button>
              <el-button
                type="danger"
                size="mini"
                @click="handleDisable"
                :disabled="!selectedVehicle">停用</el-button>
            </div>
            <!-- 新增：车辆方向控制区域 -->
            <div class="direction-control-bar" v-if="activeName === '1'">
              <el-form :inline="true" size="small">
                <el-form-item label="选择方向">
                  <el-select v-model="selectedDirection" placeholder="请选择方向" :disabled="!selectedVehicle">
                    <el-option
                      v-for="item in directions"
                      :key="item"
                      :label="item"
                      :value="item">
                    </el-option>
                  </el-select>
                </el-form-item>
                <el-form-item>
                  <el-button
                    type="primary"
                    icon="el-icon-s-promotion"
                    @click="handleControlDirection"
                    :disabled="!selectedVehicle || selectedDirection === null"
                  >发送方向指令</el-button>
                </el-form-item>
              </el-form>
            </div>
            <el-table
              :data="shebeiList"
              style="width: 100%"
              @selection-change="handleSelectionChange"
              @row-click="handleRowClick"
              ref="multipleTable">
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="vehicleCode" label="小车编号">
              </el-table-column>
              <el-table-column prop="vehicleName" label="小车名称">
              </el-table-column>
              <el-table-column prop="vehicleType" label="小车类型">
              </el-table-column>
              <el-table-column prop="vehicleStatus" label="小车状态">
              </el-table-column>
              <el-table-column prop="loadStatus" label="是否载货">
              </el-table-column>
              <el-table-column prop="batteryLevel" label="小车电量">
              </el-table-column>
              <el-table-column prop="currentPosition" label="当前位置">
              </el-table-column>
            </el-table>
          </el-tab-pane>
          <el-tab-pane label="报警监控" name="2">
            <el-table :data="errorList" style="width: 100%">
              <el-table-column prop="vehicleCode" label="车辆编号"></el-table-column>
              <el-table-column prop="errorType" label="报警类型"></el-table-column>
              <el-table-column prop="errorLevel" label="报警等级">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.errorLevel === 'error' ? 'danger' : 'warning'">
                    {{ scope.row.errorLevel === 'error' ? '严重' : '警告' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="errorTime" label="报警时间"></el-table-column>
              <el-table-column prop="errorLocation" label="报警位置"></el-table-column>
              <el-table-column prop="status" label="处理状态">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.status === '未处理' ? 'danger' : 'success'">
                    {{ scope.row.status }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="text"
                    @click="handleProcessError(scope.row)"
                    v-if="scope.row.status === '未处理'">处理</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div>
        <div class="btn">
          <el-button
            :type="cIndex == item ? 'primary' : 'info'"
            size="mini"
            v-for="item in cList"
            :key="item"
            @click="toCeng(item)"
          >第{{ item }}层</el-button
          >
        </div>
        
        <!-- 添加图例 -->
        <div class="legend-container">
          <div class="legend-title">图例说明：</div>
          <div class="legend-item">
            <div class="legend-color" style="background-color: #82c6ad;"></div>
            <div class="legend-text">空托盘位</div>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background-color: #f0f08e;"></div>
            <div class="legend-text">通道</div>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background-color: #FF0000;"></div>
            <div class="legend-text">无货禁用</div>
          </div>
          <div class="legend-item">
            <div class="legend-color" style="background-color: #FF9900;"></div>
            <div class="legend-text">有货禁用</div>
          </div>
        </div>
        
        <canvas
          id="canvas"
          ref="canvas"
          @contextmenu.prevent="handleContextMenu"
        ></canvas>
      </div>
    </div>

    <div id="menu" style="display: none; position: absolute;">
      <p
        style="cursor: pointer"
        v-if="mm.locationattribute == '0'"
        @click="menuClick('5')"
      >
        禁用(无货)
      </p>
      <p
        style="cursor: pointer"
        v-if="mm.locationattribute == '0'"
        @click="menuClick('4')"
      >
        禁用(有货)
      </p>
      <p
        style="cursor: pointer"
        v-if="mm.locationattribute == '5' || mm.locationattribute == '4'"
        @click="menuClick('0')"
      >
        空闲
      </p>
    </div>
  </div>
</template>

<script>
import {
  getKg,
  getCC,
  adminStatus,
  getClist,
  getTsjlist,
  getError,
  resetVehicle,
  enableVehicle,
  disableVehicle,
  controlVehicleDirection // 新增：控制车辆方向的API
} from "@/api/monitoring";

export default {
  name: "Van",
  data() {
    return {
      shebeiList: [],
      shebeiList1: [],
      activeName: "1",
      queryParams: {
        GRID_WIDTH: 24,
        GAP_WIDTH: 4,
        ROW: 11,
        ROC: 17,
        ceng: 5,
      },
      ctx: null,
      cList: [],
      cIndex: 1,
      tongdaoList: [],
      ktpwList: [],
      ykctpwList: [],
      fctpwList: [],
      ssjList: [],
      tsjList: [],
      cdwList: [],
      dataList: [],
      width: 0,
      height: 0,
      x: null,
      y: null,
      xx: null,
      yy: null,
      index: 1,
      image: new Image(),
      tsj: new Image(),
      jbd: new Image(),
      cdw: new Image(),
      che: new Image(),
      mm: {
        locationattribute: null,
      },
      errorList: [],
      selectedVehicle: null,
      errorTimer: null,
      mockErrorData: [
        {
          id: 1,
          vehicleCode: '1',
          errorType: '障碍物警告',
          errorLevel: 'warning',
          errorTime: '2024-03-20 10:30:00',
          errorLocation: 'X:10,Y:15',
          status: '未处理'
        },
        {
          id: 2,
          vehicleCode: '2',
          errorType: '电量低警告',
          errorLevel: 'error',
          errorTime: '2024-03-20 10:28:00',
          errorLocation: 'X:5,Y:8',
          status: '未处理'
        },
      ],
      vehiclesOnMap: [],
      normalVehicleImage: null,
      alarmVehicleImage: null,
      selectedDirection: null, // 新增：选中的方向
      directions: [1, 2, 3, 4], // 新增：方向选项
    };
  },
  mounted() {
    // 预加载车辆图像
    this.preloadVehicleImages();

    let that = this;
    that.getCeng();
    that.getCanvas();
    that.getXy();
    that.getKw();
    that.getcheList();
    this.timer = setInterval(this.draw, 1000);
    this.startErrorMonitor();
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
    if (this.errorTimer) {
      clearInterval(this.errorTimer);
    }
  },
  methods: {
    getcheList() {
      getClist().then((res) => {
        this.shebeiList = res.data;
        console.log("车辆列表数据:", this.shebeiList);

        // 获取车辆列表后尝试渲染报警车辆
        this.renderAlarmVehicles();
      }).catch(err => {
        console.error("获取车辆列表失败:", err);
      });
    },
    getTsjslist() {
      getTsjlist().then((res) => {
        this.shebeiList1 = res.data;
        console.log(res.data, "提升机列表数据");
      });
    },
    handleClick(tab) {
      if (tab.name === '1') {
        this.getcheList();
      } else if (tab.name === '2') {
        this.getErrorList();
      }
    },
    menuClick(status) {
      let obj = {
        xCoordinate: this.mm.xcoord,
        yCoordinate: this.mm.ycoord,
        zCoordinate: this.mm.zcoord,
        locationStatus: status || (this.mm.locationattribute == "0" ? "5" : "0"),
      };
      adminStatus(obj).then((res) => {
        if (res.code == 200) {
          this.getKw();
          this.$message({
            message: "操作成功",
            type: "success",
          });
        } else {
          this.$message({
            message: "操作失败",
            type: "error",
          });
        }
      });
    },
    getList() {
      this.drawSnack(this.ctx, this.ktpwList, "#82c6ad");
      this.drawSnack(this.ctx, this.tongdaoList, "#f0f08e");
      this.drawSnack(this.ctx, this.fctpwList, "#FF0000");
      this.drawSnack(this.ctx, this.yhdpwList, "#FF9900");
      this.image.src = require("./xxx.png");
      this.image.onload = () => {
        if (this.ykctpwList.length == 0) return;
        for (let i = 0; i < this.ykctpwList.length; i++) {
          this.ctx.drawImage(
            this.image,
            this.getGridULCoordinate(this.ykctpwList[i])[0],
            this.getGridULCoordinate(this.ykctpwList[i])[1],
            this.queryParams.GRID_WIDTH,
            this.queryParams.GRID_WIDTH
          );
        }
      };
      this.tsj.src = require("./tsj.png");
      this.tsj.onload = () => {
        if (this.tsjList.length == 0) return;
        for (let i = 0; i < this.tsjList.length; i++) {
          this.ctx.drawImage(
            this.tsj,
            this.getGridULCoordinate(this.tsjList[i])[0],
            this.getGridULCoordinate(this.tsjList[i])[1],
            this.queryParams.GRID_WIDTH,
            this.queryParams.GRID_WIDTH
          );
        }
      };
      this.jbd.src = require("./jbd.png");
      this.jbd.onload = () => {
        if (this.ssjList.length == 0) return;
        for (let i = 0; i < this.ssjList.length; i++) {
          this.ctx.drawImage(
            this.jbd,
            this.getGridULCoordinate(this.ssjList[i])[0],
            this.getGridULCoordinate(this.ssjList[i])[1],
            this.queryParams.GRID_WIDTH,
            this.queryParams.GRID_WIDTH
          );
        }
      };
      this.cdw.src = require("./cdw.png");
      this.cdw.onload = () => {
        if (this.cdwList.length == 0) return;
        for (let i = 0; i < this.cdwList.length; i++) {
          this.ctx.drawImage(
            this.cdw,
            this.getGridULCoordinate(this.cdwList[i])[0],
            this.getGridULCoordinate(this.cdwList[i])[1],
            this.queryParams.GRID_WIDTH,
            this.queryParams.GRID_WIDTH
          );
        }
      };
    },
    None(e) {
      const menu = document.getElementById("menu");
      if (e.target != menu) {
        menu.style.display = "none";
        this.mm = {};
      }
    },
    handleContextMenu(event) {
      const canvas = this.$refs.canvas;
      const rect = canvas.getBoundingClientRect();
      const menu = document.getElementById("menu");

      const x = event.clientX - rect.left;
      const y = event.clientY - rect.top;
      menu.style.left = event.clientX - 200 + "px";
      menu.style.top = event.clientY - 84 + "px";
      console.log(`右键点击在位置: x=${x}, y=${y}`);
      this.mm = null;
      this.dataList.forEach((e) => {
        if (e.xcoord == this.getZuobiao([x, y])[0]) {
          if (e.ycoord == this.getZuobiao([x, y])[1]) {
            this.mm = e;
            console.log(e, "111");
            menu.style.display = "block";
          }
        }
      });
      event.preventDefault();
    },
    draw() {
      // 清除上一帧所有内容
      if (this.width && this.height) {
        this.ctx.clearRect(0, 0, this.width, this.height);
      }

      // 重绘基础图层
      this.getXy();
      this.getList();
      this.getChe();

      // 渲染报警车辆（特殊处理）- 确保在其他渲染之后调用，这样报警车辆会显示在最上层
      setTimeout(() => {
        this.renderAlarmVehicles();
      }, 0);
    },
    getChe() {
      getCC().then((res) => {
        if (!Array.isArray(res.data)) {
          console.error("车辆位置数据格式错误:", res.data);
          return;
        }

        // 获取所有报警车辆编号
        const alarmVehicleCodes = this.errorList
          .filter(error => error.status === '未处理')
          .map(error => error.vehicleCode);

        console.log("报警车辆编号列表:", alarmVehicleCodes);

        // 根据楼层过滤车辆
        const currentFloorVehicles = res.data.filter(v => v.Z == this.cIndex);

        // 遍历每辆车并渲染
        currentFloorVehicles.forEach(vehicle => {
          // 确定车辆编号
          const vehicleCode = vehicle.vehicleCode || `AGV${vehicle.X}${vehicle.Y}`;

          // 检查是否有报警 - 如果是报警车辆则跳过，由renderAlarmVehicles单独处理
          if (alarmVehicleCodes.includes(vehicleCode)) {
            console.log(`跳过报警车辆的普通渲染: ${vehicleCode}`);
            return;
          }

          const x = this.getGridULCoordinate([vehicle.X, vehicle.Y])[0];
          const y = this.getGridULCoordinate([vehicle.X, vehicle.Y])[1];

          // 绘制普通车辆
          if (this.normalVehicleImage && this.normalVehicleImage.complete) {
            this.drawVehicle(this.normalVehicleImage, x, y, vehicleCode);
          } else {
            this.normalVehicleImage.onload = () => {
              this.drawVehicle(this.normalVehicleImage, x, y, vehicleCode);
            };
          }
        });
      }).catch(err => {
        console.error("获取车辆位置数据失败:", err);
      });
    },
    drawVehicle(img, x, y, vehicleCode) {
      this.ctx.drawImage(
        img,
        x,
        y,
        this.queryParams.GRID_WIDTH,
        this.queryParams.GRID_WIDTH
      );

      // 添加车辆编号
      this.ctx.fillStyle = "#FFFFFF";
      this.ctx.font = "bold 12px Arial";
      this.ctx.textAlign = "center";
      this.ctx.fillText(
        vehicleCode.slice(-3),
        x + this.queryParams.GRID_WIDTH/2,
        y + this.queryParams.GRID_WIDTH - 5
      );
    },
    toCanvas() {
      this.ctx.clearRect(0, 0, this.width, this.height);
      this.tongdaoList = [];
      this.ktpwList = [];
      this.ykctpwList = [];
      this.fctpwList = [];
      this.ssjList = [];
      this.tsjList = [];
      this.cdwList = [];
      this.dataList = [];
      this.getCeng();
      this.getCanvas();
      this.getXy();
      this.getKw();
    },
    toCeng(item) {
      this.cIndex = item;
      this.ctx.clearRect(0, 0, this.width, this.height);
      this.tongdaoList = [];
      this.ktpwList = [];
      this.ykctpwList = [];
      this.fctpwList = [];
      this.ssjList = [];
      this.tsjList = [];
      this.cdwList = [];
      this.dataList = [];
      this.getXy();
      this.getKw();
    },
    getCeng() {
      this.cList = [];
      for (let i = 1; i <= this.queryParams.ceng; i++) {
        this.cList.push(i);
      }
    },
    getKw() {
      getKg({ nLayer: this.cIndex }).then((res) => {
        console.log(res.data, "库位数据");
        this.dataList = [];
        this.tongdaoList = [];
        this.ktpwList = [];
        this.ykctpwList = [];
        this.fctpwList = [];
        this.yhdpwList = [];
        this.ssjList = [];
        this.tsjList = [];
        this.l = [];
        let data = res.data;
        this.dataList = res.data;
        console.log(data, "库位数据");
        data.forEach((e) => {
          if (e.locationcategory == "1") {
            this.tongdaoList.push([e.xcoord, e.ycoord]);
          } else if (e.locationcategory == "2") {
            if (e.locationattribute == 0 && e.isLot == "0") {
              this.ktpwList.push([e.xcoord, e.ycoord]);
            } else if (e.locationattribute == 1 && e.isLot == "1") {
              this.ykctpwList.push([e.xcoord, e.ycoord]);
            } else if (e.locationattribute == 4) {
              this.yhdpwList.push([e.xcoord, e.ycoord]);
            } else if (e.locationattribute == 5 || e.isLot == "1") {
              this.fctpwList.push([e.xcoord, e.ycoord]);
            }
          } else if (e.locationcategory == "4") {
            this.tsjList.push([e.xcoord, e.ycoord]);
          } else if (e.locationcategory == "5") {
            this.ssjList.push([e.xcoord, e.ycoord]);
          }
        });
        this.getList();
      });
    },
    getCanvas() {
      let canvas = document.getElementById("canvas");
      canvas.height =
        this.queryParams.GRID_WIDTH * this.queryParams.ROW +
        this.queryParams.GAP_WIDTH * (this.queryParams.ROW - 1);
      canvas.width =
        this.queryParams.GRID_WIDTH * this.queryParams.ROC +
        this.queryParams.GAP_WIDTH * (this.queryParams.ROC - 1);
      this.width = canvas.width;
      this.height = canvas.height;
      this.ctx = canvas.getContext("2d");
    },
    getXy() {
      let XZ = [];
      let YX = [];
      let hui = [];
      for (let i = 0; i < this.queryParams.ROW; i++) {
        XZ.push([0, i]);
      }
      for (let j = 0; j < this.queryParams.ROC; j++) {
        YX.push([j, 0]);
      }
      for (let a = 1; a < this.queryParams.ROC; a++) {
        for (let b = 1; b < this.queryParams.ROW; b++) {
          hui.push([a, b]);
        }
      }
      this.drawSnackzz(this.ctx, XZ, "#d7eee9", "y");
      this.drawSnackzz(this.ctx, YX, "#d7eee9", "x");
      this.drawSnack(this.ctx, hui, "#b9b9bb");
    },
    drawSnackzz(ctx, snack, color, axis) {
      for (let i = 0; i < snack.length; i++) {
        ctx.beginPath();
        ctx.fillStyle = color;
        ctx.fillRect(
          ...this.getGridULCoordinate(snack[i], "t"),
          this.queryParams.GRID_WIDTH,
          this.queryParams.GRID_WIDTH
        );
        ctx.fillStyle = "#000000";
        ctx.fillText(
          i,
          this.getGridULCoordinate(snack[i], "t")[0] + 6,
          this.getGridULCoordinate(snack[i], "t")[1] + 16
        );
      }
    },
    getGridULCoordinate(g, t) {
      if (t == "t") {
        return [
          g[0] * (this.queryParams.GRID_WIDTH + this.queryParams.GAP_WIDTH),
          g[1] * (this.queryParams.GRID_WIDTH + this.queryParams.GAP_WIDTH),
        ];
      }
      return [
        g[0] * (this.queryParams.GRID_WIDTH + this.queryParams.GAP_WIDTH),
        g[1] * (this.queryParams.GRID_WIDTH + this.queryParams.GAP_WIDTH),
      ];
    },
    getZuobiao(g) {
      let x = Math.round(
        g[0] / (this.queryParams.GRID_WIDTH + this.queryParams.GAP_WIDTH)
      );
      let y = Math.round(
        g[1] / (this.queryParams.GRID_WIDTH + this.queryParams.GAP_WIDTH)
      );
      if (x > 0 && y > 0) {
        return [x, y];
      }
      return [x, y];
    },
    drawSnack(ctx, snack, color) {
      ctx.fillStyle = color;
      for (let i = 0; i < snack.length; i++) {
        ctx.fillRect(
          ...this.getGridULCoordinate(snack[i]),
          this.queryParams.GRID_WIDTH,
          this.queryParams.GRID_WIDTH
        );
      }
    },
    handleSelectionChange(selection) {
      this.selectedVehicle = selection.length ? selection[0] : null;
    },
    handleRowClick(row) {
      this.$refs.multipleTable.toggleRowSelection(row);
    },
    handleReset() {
      if (!this.selectedVehicle) {
        this.$message.warning('请选择要复位的车辆');
        return;
      }
      this.$confirm('确认要复位该车辆吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        resetVehicle({ vehicleCode: this.selectedVehicle.vehicleCode }).then(res => {
          if (res.code === 200) {
            this.$message.success('复位成功');
            this.getcheList();
          } else {
            this.$message.error(res.msg || '复位失败');
          }
        });
      }).catch(() => {});
    },
    handleEnable() {
      if (!this.selectedVehicle) {
        this.$message.warning('请选择要启用的车辆');
        return;
      }
      this.$confirm('确认要启用该车辆吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        enableVehicle({ vehicleCode: this.selectedVehicle.vehicleCode }).then(res => {
          if (res.code === 200) {
            this.$message.success('启用成功');
            this.getcheList();
          } else {
            this.$message.error(res.msg || '启用失败');
          }
        });
      }).catch(() => {});
    },
    handleDisable() {
      if (!this.selectedVehicle) {
        this.$message.warning('请选择要停用的车辆');
        return;
      }
      this.$confirm('确认要停用该车辆吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        disableVehicle({ vehicleCode: this.selectedVehicle.vehicleCode }).then(res => {
          if (res.code === 200) {
            this.$message.success('停用成功');
            this.getcheList();
          } else {
            this.$message.error(res.msg || '停用失败');
          }
        });
      }).catch(() => {});
    },
    startErrorMonitor() {
      this.getErrorList();
      this.errorTimer = setInterval(() => {
        this.getErrorList();
      }, 5000);
    },
    getErrorList() {
      getError().then(res => {
        if (res.code === 200 && Array.isArray(res.data)) {
          // 使用后端数据
          this.errorList = res.data;
        } else {
          // 如果后端没有返回数据，使用模拟数据
          this.errorList = this.mockErrorData;
        }

        // 获取报警列表后尝试渲染报警车辆
        this.renderAlarmVehicles();

        // 检查是否有未处理的报警
        const hasUnprocessed = this.errorList.some(item => item.status === '未处理');
        if (hasUnprocessed) {
          this.$message({
            message: '有新的未处理报警信息',
            type: 'warning',
            duration: 3000
          });
        }
      }).catch(() => {
        console.warn('获取报警信息失败，使用模拟数据');
        this.errorList = this.mockErrorData;

        // 即使使用模拟数据也尝试渲染报警车辆
        this.renderAlarmVehicles();
      });
    },
    handleProcessError(row) {
      this.$confirm('确认要处理该报警吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const index = this.errorList.findIndex(item => item.id === row.id);
        if (index !== -1) {
          this.errorList[index].status = '已处理';
          this.$message.success('处理成功');
        }
      }).catch(() => {});
    },
    // 预加载车辆图像
    preloadVehicleImages() {
      // 加载普通车辆图像
      this.normalVehicleImage = new Image();
      this.normalVehicleImage.src = require("./che.png");

      // 加载报警车辆图像
      this.alarmVehicleImage = new Image();
      this.alarmVehicleImage.src = require("./che.png"); // 可以使用相同图像，渲染时会添加红色背景
    },
    // 渲染报警车辆的方法
    renderAlarmVehicles() {
      // 获取未处理的报警
      const alarms = this.errorList.filter(item => item.status === '未处理');

      // 如果有未处理的报警，并且已获取车辆列表
      if (alarms.length > 0 && this.shebeiList && this.shebeiList.length > 0) {
        console.log("发现未处理报警:", alarms);
        console.log("当前车辆列表:", this.shebeiList);

        // 遍历每个报警
        alarms.forEach(alarm => {
          console.log(`处理报警: ${alarm.vehicleCode}`);

          // 在车辆列表中查找匹配的车辆
          const vehicle = this.shebeiList.find(v => v.vehicleCode === alarm.vehicleCode);

          if (vehicle) {
            console.log(`找到报警车辆: ${vehicle.vehicleCode}, 位置信息:`, vehicle.currentPosition);

            // 从位置字符串中提取坐标
            let x, y;

            if (vehicle.currentPosition) {
              // 尝试多种可能的位置格式
              const posMatch1 = vehicle.currentPosition.match(/X:(\d+),Y:(\d+)/i);
              const posMatch2 = vehicle.currentPosition.match(/(\d+),(\d+)/);

              if (posMatch1) {
                x = parseInt(posMatch1[1]);
                y = parseInt(posMatch1[2]);
                console.log(`从格式1解析位置: X=${x}, Y=${y}`);
              } else if (posMatch2) {
                x = parseInt(posMatch2[1]);
                y = parseInt(posMatch2[2]);
                console.log(`从格式2解析位置: X=${x}, Y=${y}`);
              } else if (typeof vehicle.currentPosition === 'string' && vehicle.currentPosition.length === 6) {
                // 处理类似"100804"的编码格式
                x = parseInt(vehicle.currentPosition.substring(0, 2));
                y = parseInt(vehicle.currentPosition.substring(2, 4));
                const z = parseInt(vehicle.currentPosition.substring(4, 6));

                // 只有当Z坐标匹配当前楼层时才渲染
                if (z !== this.cIndex) {
                  console.log(`车辆在不同楼层，跳过渲染: 车辆在${z}层，当前在${this.cIndex}层`);
                  return;
                }

                console.log(`从编码格式解析位置: X=${x}, Y=${y}, Z=${z}`);
              } else {
                console.warn(`无法解析位置字符串: ${vehicle.currentPosition}`);
              }
            } else if (vehicle.X !== undefined && vehicle.Y !== undefined) {
              // 如果车辆对象直接包含X和Y属性
              x = vehicle.X;
              y = vehicle.Y;

              // 检查Z坐标是否匹配当前楼层
              if (vehicle.Z !== undefined && vehicle.Z !== this.cIndex) {
                console.log(`车辆在不同楼层，跳过渲染: 车辆在${vehicle.Z}层，当前在${this.cIndex}层`);
                return;
              }

              console.log(`直接使用车辆坐标: X=${x}, Y=${y}`);
            } else {
              console.warn(`车辆 ${vehicle.vehicleCode} 没有位置信息`);
            }

            // 如果成功获取到坐标
            if (x !== undefined && y !== undefined) {
              // 计算画布坐标
              const canvasX = this.getGridULCoordinate([x, y])[0];
              const canvasY = this.getGridULCoordinate([x, y])[1];

              console.log(`绘制报警车辆红色背景, 画布坐标: (${canvasX}, ${canvasY})`);

              // 绘制红色背景 - 使用更明显的红色并增加大小
              this.ctx.fillStyle = "#FF0000";
              this.ctx.fillRect(
                canvasX - 4,
                canvasY - 4,
                this.queryParams.GRID_WIDTH + 8,
                this.queryParams.GRID_WIDTH + 8
              );

              // 绘制车辆图标
              if (this.normalVehicleImage && this.normalVehicleImage.complete) {
                this.ctx.drawImage(
                  this.normalVehicleImage,
                  canvasX,
                  canvasY,
                  this.queryParams.GRID_WIDTH,
                  this.queryParams.GRID_WIDTH
                );

                // 显示车辆编号
                this.ctx.fillStyle = "#FFFFFF";
                this.ctx.font = "bold 12px Arial";
                this.ctx.textAlign = "center";
                this.ctx.fillText(
                  vehicle.vehicleCode.slice(-3),
                  canvasX + this.queryParams.GRID_WIDTH/2,
                  canvasY + this.queryParams.GRID_WIDTH - 5
                );
              }
            }
          } else {
            console.warn(`未找到报警车辆: ${alarm.vehicleCode}`);

            // 尝试从报警信息中直接获取位置
            if (alarm.errorLocation) {
              const posMatch = alarm.errorLocation.match(/X:(\d+),Y:(\d+)/i);
              if (posMatch) {
                const x = parseInt(posMatch[1]);
                const y = parseInt(posMatch[2]);

                // 计算画布坐标
                const canvasX = this.getGridULCoordinate([x, y])[0];
                const canvasY = this.getGridULCoordinate([x, y])[1];

                console.log(`从报警信息中获取位置并绘制红色背景: (${canvasX}, ${canvasY})`);

                // 绘制红色背景
                this.ctx.fillStyle = "#FF0000";
                this.ctx.fillRect(
                  canvasX - 4,
                  canvasY - 4,
                  this.queryParams.GRID_WIDTH + 8,
                  this.queryParams.GRID_WIDTH + 8
                );

                // 绘制车辆图标
                if (this.normalVehicleImage && this.normalVehicleImage.complete) {
                  this.ctx.drawImage(
                    this.normalVehicleImage,
                    canvasX,
                    canvasY,
                    this.queryParams.GRID_WIDTH,
                    this.queryParams.GRID_WIDTH
                  );

                  // 显示车辆编号
                  this.ctx.fillStyle = "#FFFFFF";
                  this.ctx.font = "bold 12px Arial";
                  this.ctx.textAlign = "center";
                  this.ctx.fillText(
                    alarm.vehicleCode.slice(-3),
                    canvasX + this.queryParams.GRID_WIDTH/2,
                    canvasY + this.queryParams.GRID_WIDTH - 5
                  );
                }
              }
            }
          }
        });
      }
    },
    // 新增：处理车辆方向控制
    handleControlDirection() {
      if (!this.selectedVehicle) {
        this.$message.warning('请选择要控制方向的车辆');
        return;
      }
      if (this.selectedDirection === null) {
        this.$message.warning('请选择方向');
        return;
      }

      const vehicleCode = this.selectedVehicle.vehicleCode;
      const direction = this.selectedDirection;

      this.$confirm(`确认要将车辆 ${vehicleCode} 的方向设置为 ${direction} 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        controlVehicleDirection({ vehicleCode, direction }).then(res => {
          if (res.code === 200) {
            this.$message.success('方向指令发送成功');
            // 可以根据需要刷新车辆列表或其他状态
            // this.getcheList();
          } else {
            this.$message.error(res.msg || '方向指令发送失败');
          }
        }).catch(err => {
           this.$message.error('方向指令发送异常');
           console.error("方向指令发送异常:", err);
        });
      }).catch(() => {});
    }
  },
  computed: {
    // 获取未处理的报警信息
    activeAlarms() {
      return this.errorList.filter(item => item.status === '未处理');
    }
  },
};
</script>

<style>
.flex {
  display: flex;
}

.left {
  width: 30%;
  margin-right: 20px;
}

#canvas {
  background: #ffffff;
}

.btn {
  margin-bottom: 10px;
}

.app-container {
  position: relative;
}

#menu {
  padding: 5px;
  box-sizing: border-box;
  border-radius: 10px;
  border: 1px solid #999999;
  background: #ffffff;
}

.operation-bar {
  margin-bottom: 10px;
}

.el-table .warning-row {
  background: #fdf5e6;
}

.el-table .error-row {
  background: #fef0f0;
}

.alarm-bar {
  background: #fff;
  margin-bottom: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
}

.alarm-header {
  padding: 10px 15px;
  border-bottom: 1px solid #ebeef5;
  display: flex;
  align-items: center;
  background: #f4f4f5;
}

.alarm-header i {
  color: #E6A23C;
  margin-right: 8px;
  font-size: 18px;
}

.alarm-header span {
  font-size: 16px;
  font-weight: bold;
  flex: 1;
}

.alarm-list {
  padding: 10px;
}

.alarm-item {
  padding: 10px 15px;
  margin-bottom: 8px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  font-size: 14px;
}

.alarm-item:last-child {
  margin-bottom: 0;
}

.warning-alarm {
  background: #fdf6ec;
  border: 1px solid #faecd8;
}

.error-alarm {
  background: #fef0f0;
  border: 1px solid #fde2e2;
}

.alarm-time {
  margin-right: 15px;
  color: #666;
}

.alarm-type {
  margin-right: 15px;
  font-weight: bold;
}

.alarm-location {
  margin-right: 15px;
  color: #666;
}

.alarm-vehicle {
  margin-right: 15px;
  color: #666;
}

.alarm-item .el-button {
  margin-left: auto;
}

.direction-control-bar {
  margin-bottom: 10px;
}

.legend-container {
  margin-bottom: 10px;
}

.legend-title {
  font-weight: bold;
  margin-bottom: 5px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
}

.legend-color {
  width: 20px;
  height: 20px;
  margin-right: 5px;
  border-radius: 4px;
}

.legend-text {
  font-size: 14px;
}
</style>
