import request from '@/utils/request'

// 查询库区表列表
export function listArea(query) {
  return request({
    url: '/base/area/list',
    method: 'get',
    params: query
  })
}

// 查询库区表详细
export function getArea(areaId) {
  return request({
    url: '/base/area/' + areaId,
    method: 'get'
  })
}

// 新增库区表
export function addArea(data) {
  return request({
    url: '/base/area',
    method: 'post',
    data: data
  })
}

// 修改库区表
export function updateArea(data) {
  return request({
    url: '/base/area',
    method: 'put',
    data: data
  })
}

// 删除库区表
export function delArea(areaId) {
  return request({
    url: '/base/area/' + areaId,
    method: 'delete'
  })
}
