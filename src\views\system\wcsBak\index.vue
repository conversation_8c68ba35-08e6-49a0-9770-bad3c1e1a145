<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务类型 " prop="taskType">
        <el-select v-model="queryParams.taskType" placeholder="请选择任务类型 " clearable>
          <el-option
            v-for="dict in dict.type.wcs_task_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-input
          v-model="queryParams.priority"
          placeholder="请输入优先级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="taskStatus">
        <el-select v-model="queryParams.taskStatus" placeholder="请选择任务状态" clearable>
          <el-option
            v-for="dict in dict.type.task_sts"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务状态回传" prop="jkSts">
        <el-select v-model="queryParams.jkSts" placeholder="请选择任务状态回传" clearable>
          <el-option
            v-for="dict in dict.type.jk_sts"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini"
          @click="handleAdd"
          v-hasPermi="['system:wcsBak:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:wcsBak:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:wcsBak:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini"
          @click="handleExport"
          v-hasPermi="['system:wcsBak:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-data-analysis" size="mini"
                   @click="toggleView"
        >{{ isTableView ? '图表视图' : '表格视图' }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格视图 -->
    <div v-if="isTableView">
      <el-table v-loading="loading" :data="wcsBakList" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="WMS任务ID" align="center" prop="wmsTaskId" />
        <el-table-column label="WCSID" align="center" prop="wcsTaskId" />
        <el-table-column label="车辆ID" align="center" prop="vehicleId" />
        <el-table-column label="任务类型 " align="center" prop="taskType">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.wcs_task_type" :value="scope.row.taskType" />
          </template>
        </el-table-column>
        <el-table-column label="优先级" align="center" prop="priority" />
        <el-table-column label="起始库位" align="center" prop="sourceLocationId" />
        <el-table-column label="目标库位" align="center" prop="targetLocationId" />
        <el-table-column label="货物信息" align="center" prop="code" />
        <el-table-column label="任务状态" align="center" prop="taskStatus">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.task_sts" :value="scope.row.taskStatus" />
          </template>
        </el-table-column>
        <el-table-column label="任务状态回传" align="center" prop="jkSts">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.jk_sts" :value="scope.row.jkSts" />
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
        <el-table-column label="错误代码" align="center" prop="errorCode" />
        <el-table-column label="错误信息" align="center" prop="errorMessage" />
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:wcsBak:edit']"
            >修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:wcsBak:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 图表视图 -->
    <div v-else class="chart-container">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>任务类型分布</span>
            </div>
            <div class="chart-wrapper">
              <div ref="taskTypeChart" style="width: 100%; height: 300px"></div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>优先级分布</span>
            </div>
            <div class="chart-wrapper">
              <div ref="priorityChart" style="width: 100%; height: 300px"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="24">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>任务状态趋势</span>
            </div>
            <div class="chart-wrapper">
              <div ref="statusTrendChart" style="width: 100%; height: 300px"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 添加或修改WCS历史对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="任务类型 " prop="taskType">
          <el-select v-model="form.taskType" placeholder="请选择任务类型 ">
            <el-option
              v-for="dict in dict.type.wcs_task_type"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-input v-model="form.priority" placeholder="请输入优先级" />
        </el-form-item>
        <el-form-item label="任务状态" prop="taskStatus">
          <el-select v-model="form.taskStatus" placeholder="请选择任务状态">
            <el-option
              v-for="dict in dict.type.task_sts"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="任务状态回传" prop="jkSts">
          <el-select v-model="form.jkSts" placeholder="请选择任务状态回传">
            <el-option
              v-for="dict in dict.type.jk_sts"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listWcsBak, getWcsBak, delWcsBak, addWcsBak, updateWcsBak } from "@/api/system/wcsBak";
import * as echarts from 'echarts';

export default {
  name: "WcsBak",
  dicts: ['wcs_task_type', 'task_sts', 'jk_sts'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // WCS历史表格数据
      wcsBakList: [],
      // 是否为表格视图
      isTableView: true,
      // 图表实例
      taskTypeChart: null,
      priorityChart: null,
      statusTrendChart: null,
      // 模拟数据
      mockData: [
        {
          wmsTaskId: 1001,
          wcsTaskId: 2001,
          vehicleId: "AGV001",
          taskType: "1",
          priority: "高",
          sourceLocationId: "A-01-01",
          targetLocationId: "B-02-03",
          code: "SKU10001",
          taskStatus: "0",
          jkSts: "1",
          createTime: "2023-08-15 10:30:00",
          remark: "紧急入库任务",
          errorCode: "",
          errorMessage: ""
        },
        {
          wmsTaskId: 1002,
          wcsTaskId: 2002,
          vehicleId: "AGV002",
          taskType: "2",
          priority: "中",
          sourceLocationId: "C-03-02",
          targetLocationId: "D-04-01",
          code: "SKU10002",
          taskStatus: "1",
          jkSts: "0",
          createTime: "2023-08-16 11:20:00",
          remark: "常规出库",
          errorCode: "",
          errorMessage: ""
        },
        {
          wmsTaskId: 1003,
          wcsTaskId: 2003,
          vehicleId: "AGV003",
          taskType: "3",
          priority: "低",
          sourceLocationId: "E-05-03",
          targetLocationId: "F-06-02",
          code: "SKU10003",
          taskStatus: "2",
          jkSts: "1",
          createTime: "2023-08-17 14:15:00",
          remark: "",
          errorCode: "",
          errorMessage: ""
        },
        {
          wmsTaskId: 1004,
          wcsTaskId: 2004,
          vehicleId: "AGV001",
          taskType: "4",
          priority: "高",
          sourceLocationId: "G-07-01",
          targetLocationId: "",
          code: "SKU10004",
          taskStatus: "0",
          jkSts: "0",
          createTime: "2023-08-18 09:45:00",
          remark: "月度盘点",
          errorCode: "",
          errorMessage: ""
        },
        {
          wmsTaskId: 1005,
          wcsTaskId: 2005,
          vehicleId: "AGV002",
          taskType: "1",
          priority: "中",
          sourceLocationId: "H-08-02",
          targetLocationId: "I-09-03",
          code: "SKU10005",
          taskStatus: "1",
          jkSts: "2",
          createTime: "2023-08-19 16:30:00",
          remark: "",
          errorCode: "E001",
          errorMessage: "库位已满"
        },
        {
          wmsTaskId: 1006,
          wcsTaskId: 2006,
          vehicleId: "AGV003",
          taskType: "2",
          priority: "高",
          sourceLocationId: "J-10-01",
          targetLocationId: "K-11-02",
          code: "SKU10006",
          taskStatus: "2",
          jkSts: "1",
          createTime: "2023-08-20 13:20:00",
          remark: "加急出库",
          errorCode: "",
          errorMessage: ""
        },
        {
          wmsTaskId: 1007,
          wcsTaskId: 2007,
          vehicleId: "AGV001",
          taskType: "3",
          priority: "低",
          sourceLocationId: "L-12-03",
          targetLocationId: "M-13-01",
          code: "SKU10007",
          taskStatus: "0",
          jkSts: "0",
          createTime: "2023-08-21 10:10:00",
          remark: "库位调整",
          errorCode: "",
          errorMessage: ""
        },
        {
          wmsTaskId: 1008,
          wcsTaskId: 2008,
          vehicleId: "AGV002",
          taskType: "4",
          priority: "中",
          sourceLocationId: "N-14-02",
          targetLocationId: "",
          code: "SKU10008",
          taskStatus: "1",
          jkSts: "2",
          createTime: "2023-08-22 15:45:00",
          remark: "",
          errorCode: "E002",
          errorMessage: "数量不符"
        }
      ],
      // 下一个ID值（用于新增数据）
      nextId: 2009,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        wmsTaskId: null,
        vehicleId: null,
        taskType: null,
        priority: null,
        sourceLocationId: null,
        targetLocationId: null,
        code: null,
        taskStatus: null,
        jkSts: null,
        createTime: null,
        errorCode: null,
        errorMessage: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        wmsTaskId: [
          { required: true, message: "WMS任务ID不能为空", trigger: "blur" }
        ],
        vehicleId: [
          { required: true, message: "不能为空", trigger: "blur" }
        ],
        taskType: [
          { required: true, message: "任务类型 不能为空", trigger: "change" }
        ],
        priority: [
          { required: true, message: "优先级不能为空", trigger: "blur" }
        ],
        sourceLocationId: [
          { required: true, message: "起始库位不能为空", trigger: "blur" }
        ],
        targetLocationId: [
          { required: true, message: "目标库位不能为空", trigger: "blur" }
        ],
        code: [
          { required: true, message: "货物信息不能为空", trigger: "blur" }
        ],
        taskStatus: [
          { required: true, message: "任务状态不能为空", trigger: "change" }
        ],
        jkSts: [
          { required: true, message: "任务状态回传不能为空", trigger: "change" }
        ],
        createTime: [
          { required: true, message: "开始时间不能为空", trigger: "blur" }
        ],
        errorCode: [
          { required: true, message: "错误代码不能为空", trigger: "blur" }
        ],
        errorMessage: [
          { required: true, message: "错误信息不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  mounted() {
    if (!this.isTableView) {
      this.initCharts();
    }
  },
  beforeDestroy() {
    // 销毁图表实例
    if (this.taskTypeChart) {
      this.taskTypeChart.dispose();
    }
    if (this.priorityChart) {
      this.priorityChart.dispose();
    }
    if (this.statusTrendChart) {
      this.statusTrendChart.dispose();
    }
  },
  methods: {
    /** 切换视图 */
    toggleView() {
      this.isTableView = !this.isTableView;
      if (!this.isTableView) {
        this.$nextTick(() => {
          this.initCharts();
        });
      }
    },
    /** 初始化图表 */
    initCharts() {
      // 初始化任务类型分布图表
      this.taskTypeChart = echarts.init(this.$refs.taskTypeChart);
      const taskTypeData = this.getTaskTypeData();
      this.taskTypeChart.setOption({
        title: {
          text: '任务类型分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: taskTypeData.map(item => item.name)
        },
        series: [
          {
            name: '任务类型',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '20',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: taskTypeData
          }
        ]
      });

      // 初始化优先级分布图表
      this.priorityChart = echarts.init(this.$refs.priorityChart);
      const priorityData = this.getPriorityData();
      this.priorityChart.setOption({
        title: {
          text: '优先级分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: priorityData.map(item => item.name)
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '任务数量',
            type: 'bar',
            data: priorityData.map(item => item.value),
            itemStyle: {
              color: function(params) {
                const colorList = ['#91cc75', '#fac858', '#ee6666'];
                return colorList[params.dataIndex % 3];
              }
            }
          }
        ]
      });

      // 初始化任务状态趋势图表
      this.statusTrendChart = echarts.init(this.$refs.statusTrendChart);
      const statusTrendData = this.getStatusTrendData();
      this.statusTrendChart.setOption({
        title: {
          text: '任务状态趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['待处理', '处理中', '已完成'],
          top: '30px'
        },
        xAxis: {
          type: 'category',
          data: statusTrendData.dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '待处理',
            type: 'line',
            data: statusTrendData.pending
          },
          {
            name: '处理中',
            type: 'line',
            data: statusTrendData.processing
          },
          {
            name: '已完成',
            type: 'line',
            data: statusTrendData.completed
          }
        ]
      });
    },
    /** 获取任务类型数据 */
    getTaskTypeData() {
      const taskTypeMap = {};
      const dictMap = {};
      
      // 创建字典映射
      if (this.dict.type.wcs_task_type) {
        this.dict.type.wcs_task_type.forEach(dict => {
          dictMap[dict.value] = dict.label;
        });
      }
      
      this.wcsBakList.forEach(item => {
        if (item.taskType) {
          const label = dictMap[item.taskType] || `类型${item.taskType}`;
          taskTypeMap[label] = (taskTypeMap[label] || 0) + 1;
        }
      });
      
      return Object.entries(taskTypeMap).map(([name, value]) => ({ name, value }));
    },
    /** 获取优先级数据 */
    getPriorityData() {
      const priorities = {};
      this.wcsBakList.forEach(item => {
        if (item.priority) {
          priorities[item.priority] = (priorities[item.priority] || 0) + 1;
        }
      });
      return Object.entries(priorities).map(([name, value]) => ({ name, value }));
    },
    /** 获取状态趋势数据 */
    getStatusTrendData() {
      // 提取有createTime的记录
      const recordsWithDate = this.wcsBakList.filter(item => item.createTime);
      
      // 获取日期列表
      const dates = [...new Set(recordsWithDate.map(item => {
        if (item.createTime && item.createTime.includes(' ')) {
          return item.createTime.split(' ')[0];
        }
        return '';
      }))].filter(date => date).sort();
      
      // 初始化数据
      const pending = new Array(dates.length).fill(0);
      const processing = new Array(dates.length).fill(0);
      const completed = new Array(dates.length).fill(0);

      // 处理数据
      recordsWithDate.forEach(item => {
        if (!item.createTime || !item.createTime.includes(' ')) return;
        
        const dateIndex = dates.indexOf(item.createTime.split(' ')[0]);
        if (dateIndex === -1) return;
        
        if (item.taskStatus === '0') {
          pending[dateIndex]++;
        } else if (item.taskStatus === '1') {
          processing[dateIndex]++;
        } else if (item.taskStatus === '2') {
          completed[dateIndex]++;
        }
      });

      return {
        dates: dates.length ? dates : ['无数据'],
        pending: pending.length ? pending : [0],
        processing: processing.length ? processing : [0],
        completed: completed.length ? completed : [0]
      };
    },
    /** 查询WCS历史列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      
      // 尝试调用后端API
      listWcsBak(this.queryParams).then(response => {
        this.wcsBakList = response.rows;
        this.total = response.total;
        this.loading = false;
        if (!this.isTableView) {
          this.$nextTick(() => {
            this.initCharts();
          });
        }
      }).catch(error => {
        console.log("后端API调用失败，使用模拟数据", error);
        
        // 使用模拟数据作为后备方案
        setTimeout(() => {
          // 过滤模拟数据
          let filteredList = [...this.mockData];
          
          // 根据查询条件过滤
          if (this.queryParams.taskType) {
            filteredList = filteredList.filter(item => 
              item.taskType === this.queryParams.taskType
            );
          }
          
          if (this.queryParams.priority) {
            filteredList = filteredList.filter(item => 
              item.priority && item.priority.includes(this.queryParams.priority)
            );
          }
          
          if (this.queryParams.taskStatus) {
            filteredList = filteredList.filter(item => 
              item.taskStatus === this.queryParams.taskStatus
            );
          }
          
          if (this.queryParams.jkSts) {
            filteredList = filteredList.filter(item => 
              item.jkSts === this.queryParams.jkSts
            );
          }
          
          if (this.queryParams.vehicleId) {
            filteredList = filteredList.filter(item => 
              item.vehicleId && item.vehicleId.includes(this.queryParams.vehicleId)
            );
          }
          
          // 计算分页
          const start = (this.queryParams.pageNum - 1) * this.queryParams.pageSize;
          const end = start + this.queryParams.pageSize;
          
          this.wcsBakList = filteredList.slice(start, end);
          this.total = filteredList.length;
          this.loading = false;
          
          if (!this.isTableView) {
            this.$nextTick(() => {
              this.initCharts();
            });
          }
        }, 500);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        wmsTaskId: null,
        wcsTaskId: null,
        vehicleId: null,
        taskType: null,
        priority: null,
        sourceLocationId: null,
        targetLocationId: null,
        code: null,
        taskStatus: null,
        jkSts: null,
        createTime: null,
        remark: null,
        errorCode: null,
        errorMessage: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.wcsTaskId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加WCS历史";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const wcsTaskId = row.wcsTaskId || this.ids[0];
      
      // 尝试调用后端API获取详情
      getWcsBak(wcsTaskId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改WCS历史";
      }).catch(error => {
        console.log("后端API调用失败，使用模拟数据", error);
        
        // 使用模拟数据作为后备方案
        setTimeout(() => {
          const item = this.mockData.find(item => item.wcsTaskId === wcsTaskId);
          if (item) {
            this.form = JSON.parse(JSON.stringify(item));
            this.open = true;
            this.title = "修改WCS历史";
          }
        }, 300);
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.wcsTaskId != null) {
            // 尝试调用后端API修改
            updateWcsBak(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.log("后端API调用失败，使用模拟数据", error);
              
              // 使用模拟数据作为后备方案
              setTimeout(() => {
                const index = this.mockData.findIndex(item => item.wcsTaskId === this.form.wcsTaskId);
                if (index !== -1) {
                  this.mockData[index] = JSON.parse(JSON.stringify(this.form));
                  this.$modal.msgSuccess("修改成功");
                  this.open = false;
                  this.getList();
                }
              }, 300);
            });
          } else {
            // 尝试调用后端API新增
            addWcsBak(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.log("后端API调用失败，使用模拟数据", error);
              
              // 使用模拟数据作为后备方案
              setTimeout(() => {
                const newItem = JSON.parse(JSON.stringify(this.form));
                newItem.wcsTaskId = this.nextId++;
                newItem.createTime = new Date().toLocaleString();
                this.mockData.unshift(newItem);
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }, 300);
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const wcsTaskIds = row.wcsTaskId || this.ids;
      this.$modal.confirm('是否确认删除WCS历史编号为"' + wcsTaskIds + '"的数据项？').then(function() {
        // 尝试调用后端API删除
        return delWcsBak(wcsTaskIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(error => {
        // 判断是否是用户取消操作
        if (error === 'cancel') {
          return;
        }
        
        console.log("后端API调用失败，使用模拟数据", error);
        
        // 使用模拟数据作为后备方案
        setTimeout(() => {
          if (Array.isArray(wcsTaskIds)) {
            this.mockData = this.mockData.filter(item => !wcsTaskIds.includes(item.wcsTaskId));
          } else {
            this.mockData = this.mockData.filter(item => item.wcsTaskId !== wcsTaskIds);
          }
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }, 300);
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      try {
        this.download('system/wcsBak/export', {
          ...this.queryParams
        }, `wcsBak_${new Date().getTime()}.xlsx`);
      } catch (error) {
        console.log("导出功能调用失败，显示模拟消息", error);
        this.$modal.msgSuccess("模拟导出功能：已导出到文件 wcsBak_" + new Date().getTime() + ".xlsx");
      }
    }
  }
};
</script>

<style scoped>
.chart-container {
  padding: 20px;
}
.chart-wrapper {
  padding: 20px;
  height: 300px;
}
.box-card {
  margin-bottom: 20px;
}
</style>
