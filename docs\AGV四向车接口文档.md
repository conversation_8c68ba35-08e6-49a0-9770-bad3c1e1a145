# AGV四向车接口文档

## 接口清单

### 1. 获取报警信息

**接口说明**：获取AGV车辆的报警信息列表

- **请求URL**: `/base/vehicle/error`
- **请求方式**: GET
- **请求参数**: 无

**响应格式**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,                           // 报警ID
      "vehicleCode": "AGV001",           // 车辆编号
      "errorType": "障碍物警告",         // 报警类型
      "errorLevel": "warning",           // 报警等级：warning=警告，error=错误
      "errorTime": "2024-03-20 10:30:00", // 报警时间
      "errorLocation": "X:10,Y:15",      // 报警位置
      "status": "未处理"                 // 处理状态：未处理/已处理
    }
  ]
}
```

### 2. 车辆复位

**接口说明**：执行车辆复位操作

- **请求URL**: `/vehicle/reset`
- **请求方式**: POST
- **请求参数**:
```json
{
  "vehicleCode": "AGV001"    // 车辆编号
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "复位成功",
  "data": null
}
```

### 3. 启用车辆

**接口说明**：启用指定车辆

- **请求URL**: `/vehicle/enable`
- **请求方式**: POST
- **请求参数**:
```json
{
  "vehicleCode": "AGV001"    // 车辆编号
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "启用成功",
  "data": null
}
```

### 4. 停用车辆

**接口说明**：停用指定车辆

- **请求URL**: `/vehicle/disable`
- **请求方式**: POST
- **请求参数**:
```json
{
  "vehicleCode": "AGV001"    // 车辆编号
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "停用成功",
  "data": null
}
```

### 5. 处理报警

**接口说明**：处理指定的报警信息

- **请求URL**: `/vehicle/error/process`
- **请求方式**: POST
- **请求参数**:
```json
{
  "errorId": 1,              // 报警ID
  "vehicleCode": "AGV001",   // 车辆编号
  "processNote": "已处理"    // 处理备注（可选）
}
```

**响应格式**:
```json
{
  "code": 200,
  "msg": "处理成功",
  "data": null
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 禁止访问 |
| 500 | 服务器内部错误 |

## 注意事项

1. 所有接口都需要在请求头中携带token进行身份验证
2. 报警等级分为两种：warning（警告）和error（错误）
3. 车辆状态变更（启用/停用）操作需要相应权限
4. 建议前端轮询获取报警信息的间隔时间不少于5秒
5. 所有时间格式均为：YYYY-MM-DD HH:mm:ss

## 数据库设计

### 1. 报警信息表 (vehicle_error)

```sql
CREATE TABLE vehicle_error (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    vehicle_code VARCHAR(50) NOT NULL COMMENT '车辆编号',
    error_type VARCHAR(100) NOT NULL COMMENT '报警类型',
    error_level VARCHAR(20) NOT NULL COMMENT '报警等级',
    error_time DATETIME NOT NULL COMMENT '报警时间',
    error_location VARCHAR(100) COMMENT '报警位置',
    status VARCHAR(20) NOT NULL COMMENT '处理状态',
    process_time DATETIME COMMENT '处理时间',
    process_note VARCHAR(255) COMMENT '处理备注',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. 车辆状态表 (vehicle_status)

```sql
CREATE TABLE vehicle_status (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    vehicle_code VARCHAR(50) NOT NULL COMMENT '车辆编号',
    status VARCHAR(20) NOT NULL COMMENT '车辆状态',
    battery_level INT COMMENT '电量',
    current_position VARCHAR(100) COMMENT '当前位置',
    is_enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用',
    last_reset_time DATETIME COMMENT '最后复位时间',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## 更新记录

| 版本号 | 更新日期 | 更新内容 | 更新人 |
|--------|----------|----------|--------|
| v1.0.0 | 2024-03-20 | 初始版本 | - | 