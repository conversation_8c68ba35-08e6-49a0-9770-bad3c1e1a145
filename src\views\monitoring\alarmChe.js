// 创建一个Canvas来生成报警车辆图像
function createAlarmVehicleImage() {
  // 创建一个离屏Canvas
  const canvas = document.createElement('canvas');
  canvas.width = 50;
  canvas.height = 50;
  const ctx = canvas.getContext('2d');
  
  // 绘制红色背景
  ctx.fillStyle = "#FF0000";
  ctx.fillRect(0, 0, 50, 50);
  
  // 绘制中间的白色区域（类似车辆形状）
  ctx.fillStyle = "#FFFFFF";
  ctx.fillRect(5, 5, 40, 40);
  
  // 绘制简单的车辆形状
  ctx.fillStyle = "#000000";
  ctx.fillRect(10, 10, 30, 30);
  
  // 返回数据URL格式的图像
  return canvas.toDataURL('image/png');
}

// 导出报警车辆图像数据
export const alarmVehicleImageUrl = createAlarmVehicleImage(); 