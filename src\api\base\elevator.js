import request from '@/utils/request'

// 查询升降机表列表
export function listElevator(query) {
  return request({
    url: '/base/elevator/list',
    method: 'get',
    params: query
  })
}

// 查询升降机表详细
export function getElevator(elevatorId) {
  return request({
    url: '/base/elevator/' + elevatorId,
    method: 'get'
  })
}

// 新增升降机表
export function addElevator(data) {
  return request({
    url: '/base/elevator',
    method: 'post',
    data: data
  })
}

// 修改升降机表
export function updateElevator(data) {
  return request({
    url: '/base/elevator',
    method: 'put',
    data: data
  })
}

// 删除升降机表
export function delElevator(elevatorId) {
  return request({
    url: '/base/elevator/' + elevatorId,
    method: 'delete'
  })
}
