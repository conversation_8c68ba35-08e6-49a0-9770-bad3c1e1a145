import request from '@/utils/request'

// 查询提升机列表
export function listHoistTask(query) {
  return request({
    url: '/system/hoistTask/list',
    method: 'get',
    params: query
  })
}

// 查询提升机详细
export function getHoistTask(code) {
  return request({
    url: '/system/hoistTask/' + code,
    method: 'get'
  })
}

// 新增提升机
export function addHoistTask(data) {
  return request({
    url: '/system/hoistTask',
    method: 'post',
    data: data
  })
}

// 修改提升机
export function updateHoistTask(data) {
  return request({
    url: '/system/hoistTask',
    method: 'put',
    data: data
  })
}

// 删除提升机
export function delHoistTask(code) {
  return request({
    url: '/system/hoistTask/' + code,
    method: 'delete'
  })
}
