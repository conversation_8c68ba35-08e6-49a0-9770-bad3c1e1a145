import request from '@/utils/request'

// 查询线路表列表
export function listLine(query) {
  return request({
    url: '/base/line/list',
    method: 'get',
    params: query
  })
}

// 查询线路表详细
export function getLine(lineId) {
  return request({
    url: '/base/line/' + lineId,
    method: 'get'
  })
}

// 新增线路表
export function addLine(data) {
  return request({
    url: '/base/line',
    method: 'post',
    data: data
  })
}

// 修改线路表
export function updateLine(data) {
  return request({
    url: '/base/line',
    method: 'put',
    data: data
  })
}

// 删除线路表
export function delLine(lineId) {
  return request({
    url: '/base/line/' + lineId,
    method: 'delete'
  })
}
