import request from '@/utils/request'

// 查询WMS任务表列表
export function listBak(query) {
  return request({
    url: 'task/wmsTaskBak/list',
    method: 'get',
    params: query
  })
}

// 查询WMS任务表详细
export function getBak(wmsTaskId) {
  return request({
    url: 'task/wmsTaskBak/' + wmsTaskId,
    method: 'get'
  })
}

// 新增WMS任务表
export function addBak(data) {
  return request({
    url: 'task/wmsTaskBak',
    method: 'post',
    data: data
  })
}

// 修改WMS任务表
export function updateBak(data) {
  return request({
    url: 'task/wmsTaskBak',
    method: 'put',
    data: data
  })
}

// 删除WMS任务表
export function delBak(wmsTaskId) {
  return request({
    url: 'task/wmsTaskBak/' + wmsTaskId,
    method: 'delete'
  })
}
