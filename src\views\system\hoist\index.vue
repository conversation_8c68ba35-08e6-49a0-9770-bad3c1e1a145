<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="模式" prop="mode">
        <el-select v-model="queryParams.mode" placeholder="请选择模式" clearable>
          <el-option
            v-for="dict in dict.type.mode"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="报警状态" prop="alarm">
        <el-select v-model="queryParams.alarm" placeholder="请选择报警状态" clearable>
          <el-option
            v-for="dict in dict.type.alarm"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="货位状态" prop="locSts">
        <el-select v-model="queryParams.locSts" placeholder="请选择货位状态" clearable>
          <el-option
            v-for="dict in dict.type.locsts"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="车状态" prop="rgvSts">
        <el-select v-model="queryParams.rgvSts" placeholder="请选择车状态" clearable>
          <el-option
            v-for="dict in dict.type.rgvsts"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="提升机任务号" prop="currentTaskId">
        <el-input
          v-model="queryParams.currentTaskId"
          placeholder="请输入提升机任务号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="链条状态" prop="chainSts">
        <el-select v-model="queryParams.chainSts" placeholder="请选择链条状态" clearable>
          <el-option
            v-for="dict in dict.type.chainsts"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="系统状态" prop="systemSts">
        <el-select v-model="queryParams.systemSts" placeholder="请选择系统状态" clearable>
          <el-option
            v-for="dict in dict.type.system_sts"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="执行状态" prop="executeSts">
        <el-input
          v-model="queryParams.executeSts"
          placeholder="请输入执行状态"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务号" prop="taskId">
        <el-input
          v-model="queryParams.taskId"
          placeholder="请输入任务号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务类型" prop="taskType">
        <el-select v-model="queryParams.taskType" placeholder="请选择任务类型" clearable>
          <el-option
            v-for="dict in dict.type.tstask_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务状态" prop="taskSts">
        <el-select v-model="queryParams.taskSts" placeholder="请选择任务状态" clearable>
          <el-option
            v-for="dict in dict.type.tstasksts"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="交互类型" prop="interactiveType">
        <el-select v-model="queryParams.interactiveType" placeholder="请选择交互类型" clearable>
          <el-option
            v-for="dict in dict.type.jhtp"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="任务更新" prop="taskUpdate">
        <el-select v-model="queryParams.taskUpdate" placeholder="请选择任务更新" clearable>
          <el-option
            v-for="dict in dict.type.taskupdate"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="交互更新" prop="interactiveUpdate">
        <el-select v-model="queryParams.interactiveUpdate" placeholder="请选择交互更新" clearable>
          <el-option
            v-for="dict in dict.type.jhupdate"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangecreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini"
          @click="handleAdd"
          v-hasPermi="['system:hoist:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:hoist:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:hoist:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini"
          @click="handleExport"
          v-hasPermi="['system:hoist:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="hoistList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="提升机编号" align="center" prop="id" />
      <el-table-column label="模式" align="center" prop="mode">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.mode" :value="scope.row.mode" />
        </template>
      </el-table-column>
      <el-table-column label="报警状态" align="center" prop="alarm">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.alarm" :value="scope.row.alarm" />
        </template>
      </el-table-column>
      <el-table-column label="货位状态" align="center" prop="locSts">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.locsts" :value="scope.row.locSts" />
        </template>
      </el-table-column>
      <el-table-column label="车状态" align="center" prop="rgvSts">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.rgvsts" :value="scope.row.rgvSts" />
        </template>
      </el-table-column>
      <el-table-column label="提升机任务号" align="center" prop="currentTaskId" />
      <el-table-column label="总层数" align="center" prop="totalLayer" />
      <el-table-column label="当前层" align="center" prop="currentLayer" />
      <el-table-column label="提升机当前坐标" align="center" prop="currentLoc" />
      <el-table-column label="链条状态" align="center" prop="chainSts">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.chainsts" :value="scope.row.chainSts" />
        </template>
      </el-table-column>
      <el-table-column label="系统状态" align="center" prop="systemSts">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.system_sts" :value="scope.row.systemSts" />
        </template>
      </el-table-column>
      <el-table-column label="执行状态" align="center" prop="executeSts" />
      <el-table-column label="任务号" align="center" prop="taskId" />
      <el-table-column label="任务类型" align="center" prop="taskType">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.tstask_type" :value="scope.row.taskType" />
        </template>
      </el-table-column>
      <el-table-column label="任务状态" align="center" prop="taskSts">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.tstasksts" :value="scope.row.taskSts" />
        </template>
      </el-table-column>
      <el-table-column label="交互类型" align="center" prop="interactiveType">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.jhtp" :value="scope.row.interactiveType" />
        </template>
      </el-table-column>
      <el-table-column label="任务更新" align="center" prop="taskUpdate">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.taskupdate" :value="scope.row.taskUpdate" />
        </template>
      </el-table-column>
      <el-table-column label="交互更新" align="center" prop="interactiveUpdate">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.jhupdate" :value="scope.row.interactiveUpdate" />
        </template>
      </el-table-column>
      <el-table-column label="占用" align="center" prop="occupy" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:hoist:edit']"
          >修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:hoist:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改提升机任务对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="模式" prop="mode">
          <el-select v-model="form.mode" placeholder="请选择模式">
            <el-option
              v-for="dict in dict.type.mode"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="报警状态" prop="alarm">
          <el-select v-model="form.alarm" placeholder="请选择报警状态">
            <el-option
              v-for="dict in dict.type.alarm"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="货位状态" prop="locSts">
          <el-select v-model="form.locSts" placeholder="请选择货位状态">
            <el-option
              v-for="dict in dict.type.locsts"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="车状态" prop="rgvSts">
          <el-select v-model="form.rgvSts" placeholder="请选择车状态">
            <el-option
              v-for="dict in dict.type.rgvsts"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="提升机任务号" prop="currentTaskId">
          <el-input v-model="form.currentTaskId" placeholder="请输入提升机任务号" />
        </el-form-item>
        <el-form-item label="链条状态" prop="chainSts">
          <el-select v-model="form.chainSts" placeholder="请选择链条状态">
            <el-option
              v-for="dict in dict.type.chainsts"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="系统状态" prop="systemSts">
          <el-select v-model="form.systemSts" placeholder="请选择系统状态">
            <el-option
              v-for="dict in dict.type.system_sts"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="执行状态" prop="executeSts">
          <el-input v-model="form.executeSts" placeholder="请输入执行状态" />
        </el-form-item>
        <el-form-item label="任务号" prop="taskId">
          <el-input v-model="form.taskId" placeholder="请输入任务号" />
        </el-form-item>
        <el-form-item label="任务类型" prop="taskType">
          <el-select v-model="form.taskType" placeholder="请选择任务类型">
            <el-option
              v-for="dict in dict.type.tstask_type"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="任务状态" prop="taskSts">
          <el-select v-model="form.taskSts" placeholder="请选择任务状态">
            <el-option
              v-for="dict in dict.type.tstasksts"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交互类型" prop="interactiveType">
          <el-select v-model="form.interactiveType" placeholder="请选择交互类型">
            <el-option
              v-for="dict in dict.type.jhtp"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="任务更新" prop="taskUpdate">
          <el-select v-model="form.taskUpdate" placeholder="请选择任务更新">
            <el-option
              v-for="dict in dict.type.taskupdate"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="交互更新" prop="interactiveUpdate">
          <el-select v-model="form.interactiveUpdate" placeholder="请选择交互更新">
            <el-option
              v-for="dict in dict.type.jhupdate"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listHoist, getHoist, delHoist, addHoist, updateHoist } from "@/api/system/hoist";

export default {
  name: "Hoist",
  dicts: ['mode', 'alarm', 'locsts', 'rgvsts', 'chainsts', 'system_sts', 'tstask_type', 'tstasksts', 'jhtp', 'taskupdate', 'jhupdate'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 提升机任务表格数据
      hoistList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 备注时间范围
      daterangecreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        mode: null,
        alarm: null,
        locSts: null,
        rgvSts: null,
        currentTaskId: null,
        totalLayer: null,
        currentLayer: null,
        currentLoc: null,
        chainSts: null,
        systemSts: null,
        executeSts: null,
        taskId: null,
        taskType: null,
        taskSts: null,
        interactiveType: null,
        taskUpdate: null,
        interactiveUpdate: null,
        occupy: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        mode: [
          { required: true, message: "模式不能为空", trigger: "change" }
        ],
        alarm: [
          { required: true, message: "报警状态不能为空", trigger: "change" }
        ],
        locSts: [
          { required: true, message: "货位状态不能为空", trigger: "change" }
        ],
        rgvSts: [
          { required: true, message: "车状态不能为空", trigger: "change" }
        ],
        currentTaskId: [
          { required: true, message: "提升机任务号不能为空", trigger: "blur" }
        ],
        totalLayer: [
          { required: true, message: "总层数不能为空", trigger: "blur" }
        ],
        currentLayer: [
          { required: true, message: "当前层不能为空", trigger: "blur" }
        ],
        currentLoc: [
          { required: true, message: "提升机当前坐标不能为空", trigger: "blur" }
        ],
        chainSts: [
          { required: true, message: "链条状态不能为空", trigger: "change" }
        ],
        systemSts: [
          { required: true, message: "系统状态不能为空", trigger: "change" }
        ],
        executeSts: [
          { required: true, message: "执行状态不能为空", trigger: "blur" }
        ],
        taskId: [
          { required: true, message: "任务号不能为空", trigger: "blur" }
        ],
        taskType: [
          { required: true, message: "任务类型不能为空", trigger: "change" }
        ],
        taskSts: [
          { required: true, message: "任务状态不能为空", trigger: "change" }
        ],
        interactiveType: [
          { required: true, message: "交互类型不能为空", trigger: "change" }
        ],
        taskUpdate: [
          { required: true, message: "任务更新不能为空", trigger: "change" }
        ],
        interactiveUpdate: [
          { required: true, message: "交互更新不能为空", trigger: "change" }
        ],
        occupy: [
          { required: true, message: "占用不能为空", trigger: "blur" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询提升机任务列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangecreateTime && '' != this.daterangecreateTime) {
        this.queryParams.params["begincreateTime"] = this.daterangecreateTime[0];
        this.queryParams.params["endcreateTime"] = this.daterangecreateTime[1];
      }
      listHoist(this.queryParams).then(response => {
        this.hoistList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        mode: null,
        alarm: null,
        locSts: null,
        rgvSts: null,
        currentTaskId: null,
        totalLayer: null,
        currentLayer: null,
        currentLoc: null,
        chainSts: null,
        systemSts: null,
        executeSts: null,
        taskId: null,
        taskType: null,
        taskSts: null,
        interactiveType: null,
        taskUpdate: null,
        interactiveUpdate: null,
        occupy: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        remark: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangecreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加提升机任务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids;
      getHoist(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改提升机任务";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            updateHoist(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHoist(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除提升机任务编号为"' + ids + '"的数据项？').then(function() {
        return delHoist(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/hoist/export', {
        ...this.queryParams
      }, `hoist_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>

<style scoped>
.el-form-item {
  margin-bottom: 18px;
  min-width: 250px;
}
.el-form-item__label {
  white-space: nowrap;
}
.el-select {
  width: 100%;
}
</style>
