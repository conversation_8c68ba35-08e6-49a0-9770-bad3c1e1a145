<template>
  <div class="app-container">
    <div class="search-container">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="80px">
        <el-form-item label="任务类型" prop="taskType">
          <el-select v-model="queryParams.taskType" placeholder="请选择任务类型" clearable>
            <el-option
              v-for="dict in dict.type.wcs_task_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="任务状态" prop="taskStatus">
          <el-select v-model="queryParams.taskStatus" placeholder="请选择任务状态" clearable>
            <el-option
              v-for="dict in dict.type.task_sts"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="接口状态" prop="jkSts">
          <el-select v-model="queryParams.jkSts" placeholder="请选择接口状态" clearable>
            <el-option
              v-for="dict in dict.type.jk_sts"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="daterangeCreateTime"
            style="width: 240px"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          ></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="action-container">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini"
                   @click="handleAdd"
                   v-hasPermi="['system:task:add']"
          >新增任务</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
                   @click="handleUpdate"
                   v-hasPermi="['system:task:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
                   @click="handleDelete"
                   v-hasPermi="['system:task:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini"
                   @click="handleExport"
                   v-hasPermi="['system:task:export']"
          >导出</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" plain icon="el-icon-data-analysis" size="mini"
                   @click="toggleView"
          >{{ isTableView ? '图表视图' : '表格视图' }}</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
    </div>

    <!-- 表格视图 -->
    <div v-if="isTableView" class="table-container">
      <el-table
        v-loading="loading"
        :data="taskList"
        row-key="wmsTaskId"
        border
        stripe
        highlight-current-row
        :tree-props="{children: 'children'}"
        :row-class-name="tableRowClassName"
        style="width: 100%"
        @row-click="handleRowClick"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="WMS任务ID" align="center" prop="wmsTaskId" min-width="80" />
        <el-table-column label="类型" align="center" width="80">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.level === 2" type="warning">备份任务</el-tag>
            <el-tag v-else type="success">主任务</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="任务类型" align="center" prop="taskType" min-width="80">
          <template slot-scope="scope">
            <dict-tag v-if="scope.row.taskType" :options="dict.type.wcs_task_type" :value="scope.row.taskType" />
            <span v-else>{{ scope.row.taskType }}</span>
          </template>
        </el-table-column>
        <el-table-column label="优先级" align="center" prop="priority" width="70"/>
        <el-table-column label="起始库位" align="center" prop="sourceLocationId" min-width="80" />
        <el-table-column label="目标库位" align="center" prop="targetLocationId" min-width="80" />
        <el-table-column label="货物信息" align="center" prop="code" min-width="80" />
        <el-table-column label="任务状态" align="center" prop="taskStatus" min-width="80">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.task_sts" :value="scope.row.taskStatus" />
          </template>
        </el-table-column>
        <el-table-column label="接口状态" align="center" prop="jkSts" min-width="80">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.jk_sts" :value="scope.row.jkSts" />
          </template>
        </el-table-column>
        <el-table-column label="错误代码" align="center" prop="errorCode" min-width="80" />
        <el-table-column label="错误信息" align="center" prop="errorMessage" min-width="100" :show-overflow-tooltip="true" />
        <el-table-column label="备注" align="center" prop="remark" min-width="100" :show-overflow-tooltip="true" />
        <el-table-column label="创建时间" align="center" prop="createTime" min-width="140">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150" fixed="right">
          <template slot-scope="scope">
            <el-tooltip content="修改" placement="top">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)" 
                v-hasPermi="['system:task:edit']"/>
            </el-tooltip>
            <el-tooltip content="删除" placement="top">
              <el-button v-if="scope.row.level === 2" size="mini" type="text" icon="el-icon-delete" @click="handleBackupDelete(scope.row)"
                v-hasPermi="['system:bak:remove']"/>
              <el-button v-else size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
                v-hasPermi="['system:task:remove']"/>
            </el-tooltip>
            <el-tooltip content="添加备份" placement="top" v-if="!scope.row.level || scope.row.level === 1">
              <el-button size="mini" type="text" icon="el-icon-copy-document" @click="handleAddBackup(scope.row)"
                v-hasPermi="['system:bak:add']"/>
            </el-tooltip>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 图表视图 -->
    <div v-else class="chart-container">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>任务类型分布</span>
            </div>
            <div class="chart-wrapper">
              <div ref="taskTypeChart" style="width: 100%; height: 300px"></div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>优先级分布</span>
            </div>
            <div class="chart-wrapper">
              <div ref="priorityChart" style="width: 100%; height: 300px"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="24">
          <el-card class="box-card">
            <div slot="header" class="clearfix">
              <span>任务状态趋势</span>
            </div>
            <div class="chart-wrapper">
              <div ref="statusTrendChart" style="width: 100%; height: 300px"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 添加或修改WMS主任务对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="任务类型" prop="taskType" v-if="form.level !== 2">
          <el-select v-model="form.taskType" placeholder="请选择任务类型">
            <el-option
              v-for="dict in dict.type.wcs_task_type"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priority" v-if="form.level !== 2">
          <el-input v-model="form.priority" placeholder="请输入优先级" />
        </el-form-item>
        <el-form-item label="起始库位" prop="sourceLocationId" v-if="form.level !== 2">
          <el-input v-model="form.sourceLocationId" placeholder="请输入起始库位" />
        </el-form-item>
        <el-form-item label="目标库位" prop="targetLocationId" v-if="form.level !== 2">
          <el-input v-model="form.targetLocationId" placeholder="请输入目标库位" />
        </el-form-item>
        <el-form-item label="货物信息" prop="code" v-if="form.level !== 2">
          <el-input v-model="form.code" placeholder="请输入货物信息" />
        </el-form-item>
        <el-form-item label="任务状态" prop="taskStatus">
          <el-select v-model="form.taskStatus" placeholder="请选择任务状态">
            <el-option
              v-for="dict in dict.type.task_sts"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="接口状态" prop="jkSts">
          <el-select v-model="form.jkSts" placeholder="请选择接口状态">
            <el-option
              v-for="dict in dict.type.jk_sts"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="错误代码" prop="errorCode">
          <el-input v-model="form.errorCode" placeholder="请输入错误代码" />
        </el-form-item>
        <el-form-item label="错误信息" prop="errorMessage">
          <el-input v-model="form.errorMessage" placeholder="请输入错误信息" type="textarea" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" type="textarea" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import {listTask, getTask, delTask, addTask, updateTask} from "@/api/system/task";
import {listBak, getBak, delBak, addBak, updateBak} from "@/api/system/bak";
import * as echarts from 'echarts';

export default {
  name: "Task",
  dicts: ['task_sts', 'jk_sts', 'wcs_task_type'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // WMS任务表表格数据
      taskList: [],
      // 是否为表格视图
      isTableView: true,
      // 图表实例
      taskTypeChart: null,
      priorityChart: null,
      statusTrendChart: null,
      // 日期范围
      daterangeCreateTime: [],
      // 模拟数据 - 主任务
      mockData: [
        {
          wmsTaskId: 1001,
          taskType: "1",
          priority: "高",
          sourceLocationId: "A-01-01",
          targetLocationId: "B-02-03",
          code: "SKU10001",
          taskStatus: "0",
          jkSts: "1",
          createTime: "2023-08-15 10:30:00",
          remark: "紧急入库任务",
          hasBackup: true
        },
        {
          wmsTaskId: 1002,
          taskType: "2",
          priority: "中",
          sourceLocationId: "C-03-02",
          targetLocationId: "D-04-01",
          code: "SKU10002",
          taskStatus: "1",
          jkSts: "0",
          createTime: "2023-08-16 11:20:00",
          remark: "常规出库",
          hasBackup: false
        },
        {
          wmsTaskId: 1003,
          taskType: "3",
          priority: "低",
          sourceLocationId: "E-05-03",
          targetLocationId: "F-06-02",
          code: "SKU10003",
          taskStatus: "2",
          jkSts: "1",
          createTime: "2023-08-17 14:15:00",
          remark: "",
          hasBackup: true
        },
        {
          wmsTaskId: 1004,
          taskType: "4",
          priority: "高",
          sourceLocationId: "G-07-01",
          targetLocationId: "",
          code: "SKU10004",
          taskStatus: "0",
          jkSts: "0",
          createTime: "2023-08-18 09:45:00",
          remark: "月度盘点",
          hasBackup: false
        }
      ],
      // 模拟数据 - 备份任务
      mockBackupData: [
        {
          wmsTaskId: 2001,
          parentId: 1001,
          taskType: "1",
          priority: "高",
          sourceLocationId: "A-01-01",
          targetLocationId: "B-02-03",
          code: "SKU10001",
          taskStatus: "1",
          jkSts: "2",
          createTime: "2023-08-15 11:30:00",
          remark: "紧急入库任务备份",
          errorCode: "E001",
          errorMessage: "库位已满"
        },
        {
          wmsTaskId: 2002,
          parentId: 1003,
          taskType: "3",
          priority: "低",
          sourceLocationId: "E-05-03",
          targetLocationId: "F-06-02",
          code: "SKU10003",
          taskStatus: "0",
          jkSts: "1",
          createTime: "2023-08-17 15:20:00",
          remark: "移库任务备份",
          errorCode: "E002",
          errorMessage: "搬运设备繁忙"
        },
        {
          wmsTaskId: 2003,
          parentId: 1001,
          taskType: "1",
          priority: "高",
          sourceLocationId: "A-01-01",
          targetLocationId: "B-02-03",
          code: "SKU10001",
          taskStatus: "2",
          jkSts: "5",
          createTime: "2023-08-15 12:45:00",
          remark: "紧急入库任务再次备份",
          errorCode: "",
          errorMessage: ""
        }
      ],
      // 下一个ID值（用于新增数据）
      nextId: 1005,
      nextBackupId: 2004,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 当前父任务ID（用于添加备份任务）
      currentParentId: null,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        wmsTaskId: null,
        taskType: null,
        priority: null,
        sourceLocationId: null,
        targetLocationId: null,
        code: null,
        taskStatus: null,
        jkSts: null,
        remark: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        taskType: [
          {required: true, message: "任务类型不能为空", trigger: "change"}
        ],
        priority: [
          {required: true, message: "优先级不能为空", trigger: "blur"}
        ],
        sourceLocationId: [
          {required: true, message: "起始库位不能为空", trigger: "blur"}
        ],
        targetLocationId: [
          {required: true, message: "目标库位不能为空", trigger: "blur"}
        ],
        code: [
          {required: true, message: "货物信息不能为空", trigger: "blur"}
        ],
        taskStatus: [
          {
            required: true, message: "任务状态不能为空",
            trigger: "change"
          }
        ],
        jkSts: [
          {
            required: true,
            message: "接口状态不能为空",
            trigger: "change"
          }
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  mounted() {
    if (!this.isTableView) {
      this.initCharts();
    }
  },
  beforeDestroy() {
    // 销毁图表实例
    if (this.taskTypeChart) {
      this.taskTypeChart.dispose();
    }
    if (this.priorityChart) {
      this.priorityChart.dispose();
    }
    if (this.statusTrendChart) {
      this.statusTrendChart.dispose();
    }
  },
  methods: {
    /** 切换视图 */
    toggleView() {
      this.isTableView = !this.isTableView;
      if (!this.isTableView) {
        this.$nextTick(() => {
          this.initCharts();
        });
      }
    },
    /** 初始化图表 */
    initCharts() {
      // 初始化任务类型分布图表
      this.taskTypeChart = echarts.init(this.$refs.taskTypeChart);
      const taskTypeData = this.getTaskTypeData();
      this.taskTypeChart.setOption({
        title: {
          text: '任务类型分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left',
          data: taskTypeData.map(item => item.name)
        },
        series: [
          {
            name: '任务类型',
            type: 'pie',
            radius: ['50%', '70%'],
            avoidLabelOverlap: false,
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '20',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: taskTypeData
          }
        ]
      });

      // 初始化优先级分布图表
      this.priorityChart = echarts.init(this.$refs.priorityChart);
      const priorityData = this.getPriorityData();
      this.priorityChart.setOption({
        title: {
          text: '优先级分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        xAxis: {
          type: 'category',
          data: priorityData.map(item => item.name)
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '任务数量',
            type: 'bar',
            data: priorityData.map(item => item.value),
            itemStyle: {
              color: function(params) {
                const colorList = ['#91cc75', '#fac858', '#ee6666'];
                return colorList[params.dataIndex % 3];
              }
            }
          }
        ]
      });

      // 初始化任务状态趋势图表
      this.statusTrendChart = echarts.init(this.$refs.statusTrendChart);
      const statusTrendData = this.getStatusTrendData();
      this.statusTrendChart.setOption({
        title: {
          text: '任务状态趋势',
          left: 'center'
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['待处理', '处理中', '已完成'],
          top: '30px'
        },
        xAxis: {
          type: 'category',
          data: statusTrendData.dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '待处理',
            type: 'line',
            data: statusTrendData.pending
          },
          {
            name: '处理中',
            type: 'line',
            data: statusTrendData.processing
          },
          {
            name: '已完成',
            type: 'line',
            data: statusTrendData.completed
          }
        ]
      });
    },
    /** 获取任务类型数据 */
    getTaskTypeData() {
      const taskTypeMap = {};
      const dictMap = {};

      // 创建字典映射
      if (this.dict.type.wcs_task_type) {
        this.dict.type.wcs_task_type.forEach(dict => {
          dictMap[dict.value] = dict.label;
        });
      }

      // 只统计主任务类型分布
      const mainTasks = this.taskList.filter(task => !task.isBackup);
      mainTasks.forEach(item => {
        if (item.taskType) {
          const label = dictMap[item.taskType] || `类型${item.taskType}`;
          taskTypeMap[label] = (taskTypeMap[label] || 0) + 1;
        }
      });

      return Object.entries(taskTypeMap).map(([name, value]) => ({ name, value }));
    },
    /** 获取优先级数据 */
    getPriorityData() {
      const priorities = {};
      // 只统计主任务优先级分布
      const mainTasks = this.taskList.filter(task => !task.isBackup);
      mainTasks.forEach(item => {
        if (item.priority) {
          priorities[item.priority] = (priorities[item.priority] || 0) + 1;
        }
      });
      return Object.entries(priorities).map(([name, value]) => ({ name, value }));
    },
    /** 获取状态趋势数据 */
    getStatusTrendData() {
      // 获取所有任务（主任务 + 备份任务）
      const allTasks = [...this.taskList];
      this.taskList.forEach(task => {
        if (task.backupTasks && task.backupTasks.length) {
          allTasks.push(...task.backupTasks);
        }
      });

      // 提取有createTime的记录
      const recordsWithDate = allTasks.filter(item => item.createTime);

      // 获取日期列表
      const dates = [...new Set(recordsWithDate.map(item => {
        if (item.createTime && item.createTime.includes(' ')) {
          return item.createTime.split(' ')[0];
        }
        return '';
      }))].filter(date => date).sort();

      // 初始化数据
      const pending = new Array(dates.length).fill(0);
      const processing = new Array(dates.length).fill(0);
      const completed = new Array(dates.length).fill(0);

      // 处理数据
      recordsWithDate.forEach(item => {
        if (!item.createTime || !item.createTime.includes(' ')) return;

        const dateIndex = dates.indexOf(item.createTime.split(' ')[0]);
        if (dateIndex === -1) return;

        if (item.taskStatus === '0') {
          pending[dateIndex]++;
        } else if (item.taskStatus === '1') {
          processing[dateIndex]++;
        } else if (item.taskStatus === '2') {
          completed[dateIndex]++;
        }
      });

      return {
        dates: dates.length ? dates : ['无数据'],
        pending: pending.length ? pending : [0],
        processing: processing.length ? processing : [0],
        completed: completed.length ? completed : [0]
      };
    },
    /** 查询WMS任务表列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }

      // 尝试调用后端API
      Promise.all([
        this.fetchTaskData(),
        this.getBackupTasks()
      ]).then(([tasks, backups]) => {
        this.processTaskAndBackupData(tasks, backups);
      }).catch(error => {
        console.log("后端API调用失败，使用模拟数据", error);

        // 使用模拟数据作为后备方案
        this.processTaskAndBackupData(this.mockData, this.mockBackupData);
      });
    },

    // 获取主任务数据
    fetchTaskData() {
      return new Promise((resolve, reject) => {
        listTask(this.queryParams).then(response => {
          resolve(response.rows || []);
        }).catch(err => {
          console.log("获取主任务失败", err);
          reject(err);
        });
      });
    },

    // 获取备份任务数据
    fetchBackupData() {
      return new Promise((resolve, reject) => {
        listBak(this.queryParams).then(response => {
          resolve(response.rows || []);
        }).catch(err => {
          console.log("获取备份任务失败", err);
          reject(err);
        });
      });
    },

    // 处理主任务和备份任务数据
    processTaskAndBackupData(tasks, backups) {
      // 为主任务添加备份任务作为子节点
      const mainTasks = tasks.map(task => {
        // 找出此任务对应的所有备份任务
        const taskBackups = backups.filter(backup => backup.wmsTaskId === task.wmsTaskId);

        // 处理备份任务数据，确保所有字段完整
        const processedBackups = taskBackups.map(backup => {
          return {
            ...backup,
            level: 2, // 标记为第二层级
            // 确保所有字段存在，如果备份任务中没有相应字段，则从主任务中继承
            wmsTaskId: backup.wmsTaskId || task.wmsTaskId,
            taskType: backup.taskType || task.taskType,
            priority: backup.priority || task.priority,
            sourceLocationId: backup.sourceLocationId || task.sourceLocationId,
            targetLocationId: backup.targetLocationId || task.targetLocationId,
            code: backup.code || task.code,
            taskStatus: backup.taskStatus,
            jkSts: backup.jkSts,
            errorCode: backup.errorCode || '',
            errorMessage: backup.errorMessage || '',
            remark: backup.remark || '',
            createTime: backup.createTime || task.createTime
          };
        });

        return {
          ...task,
          level: 1, // 标记为第一层级
          children: processedBackups.length > 0 ? processedBackups : null // 如果有备份任务则添加为children
        };
      });

      // 过滤任务列表
      let filteredList = [...mainTasks];

      // 根据查询条件过滤
      if (this.queryParams.taskType) {
        filteredList = filteredList.filter(item =>
          item.taskType === this.queryParams.taskType
        );
      }

      if (this.queryParams.taskStatus) {
        filteredList = filteredList.filter(item =>
          item.taskStatus === this.queryParams.taskStatus
        );
      }

      if (this.queryParams.jkSts) {
        filteredList = filteredList.filter(item =>
          item.jkSts === this.queryParams.jkSts
        );
      }

      // 日期范围过滤
      if (this.daterangeCreateTime && this.daterangeCreateTime.length === 2) {
        const beginTime = new Date(this.daterangeCreateTime[0]).getTime();
        const endTime = new Date(this.daterangeCreateTime[1]).getTime();

        filteredList = filteredList.filter(item => {
          const itemTime = new Date(item.createTime).getTime();
          return itemTime >= beginTime && itemTime <= endTime;
        });
      }

      // 计算分页
      const start = (this.queryParams.pageNum - 1) * this.queryParams.pageSize;
      const end = start + this.queryParams.pageSize;

      this.taskList = filteredList.slice(start, end);
      this.total = filteredList.length;
      this.loading = false;

      if (!this.isTableView) {
        this.$nextTick(() => {
          this.initCharts();
        });
      }
    },

    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        wmsTaskId: null,
        taskType: null,
        priority: null,
        sourceLocationId: null,
        targetLocationId: null,
        code: null,
        taskStatus: null,
        jkSts: null,
        createTime: null,
        remark: null,
        errorCode: null,
        errorMessage: null,
        level: 1, // 默认为主任务层级
        parentId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.wmsTaskId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加WMS任务";
    },
    /** 新增备份任务按钮操作 */
    handleAddBackup(row) {
      this.reset();
      this.form.wmsTaskId = row.wmsTaskId;
      this.form.level = 2; // 标记为备份任务层级

      // 预填充从主任务复制的所有信息
      this.form.taskType = row.taskType;
      this.form.priority = row.priority;
      this.form.sourceLocationId = row.sourceLocationId;
      this.form.targetLocationId = row.targetLocationId;
      this.form.code = row.code;
      // 默认设置相同的状态和接口状态
      this.form.taskStatus = row.taskStatus;
      this.form.jkSts = row.jkSts;
      // 错误信息和备注为空，可由用户填写
      this.form.errorCode = '';
      this.form.errorMessage = '';
      this.form.remark = '备份自任务: ' + row.wmsTaskId;

      this.open = true;
      this.title = "添加备份任务 (关联主任务ID: " + row.wmsTaskId + ")";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const wmsTaskId = row.wmsTaskId || this.ids[0];
      const isBackup = row.level === 2; // 通过level判断是否为备份任务

      // 根据任务类型（主任务/备份任务）调用不同的API
      const apiMethod = isBackup ? getBak : getTask;

      // 尝试调用后端API获取详情
      apiMethod(wmsTaskId).then(response => {
        this.form = response.data;
        this.form.level = row.level; // 保存level信息
        this.open = true;
        this.title = isBackup ? "修改备份任务" : "修改WMS任务";
      }).catch(error => {
        console.log("后端API调用失败，使用模拟数据", error);

        // 使用模拟数据作为后备方案
        setTimeout(() => {
          let item;
          if (isBackup) {
            item = this.mockBackupData.find(item => item.wmsTaskId === wmsTaskId);
          } else {
            item = this.mockData.find(item => item.wmsTaskId === wmsTaskId);
          }

          if (item) {
            this.form = JSON.parse(JSON.stringify(item));
            this.form.level = row.level; // 保存level信息
            this.open = true;
            this.title = isBackup ? "修改备份任务" : "修改WMS任务";
          }
        }, 300);
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 根据任务类型（主任务/备份任务）调用不同的API
          const isBackup = this.form.level === 2;
          const addApi = isBackup ? addBak : addTask;
          const updateApi = isBackup ? updateBak : updateTask;

          if (this.form.wmsTaskId != null) {
            // 修改操作
            updateApi(this.form).then(response => {
              this.$modal.msgSuccess(isBackup ? "修改备份任务成功" : "修改主任务成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.log("后端API调用失败，使用模拟数据", error);

              // 使用模拟数据作为后备方案
              setTimeout(() => {
                if (isBackup) {
                  const index = this.mockBackupData.findIndex(item => item.wmsTaskId === this.form.wmsTaskId);
                  if (index !== -1) {
                    this.mockBackupData[index] = JSON.parse(JSON.stringify(this.form));
                  }
                } else {
                  const index = this.mockData.findIndex(item => item.wmsTaskId === this.form.wmsTaskId);
                  if (index !== -1) {
                    this.mockData[index] = JSON.parse(JSON.stringify(this.form));
                  }
                }

                this.$modal.msgSuccess(isBackup ? "修改备份任务成功" : "修改主任务成功");
                this.open = false;
                this.getList();
              }, 300);
            });
          } else {
            // 新增操作
            addApi(this.form).then(response => {
              this.$modal.msgSuccess(isBackup ? "新增备份任务成功" : "新增主任务成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.log("后端API调用失败，使用模拟数据", error);

              // 使用模拟数据作为后备方案
              setTimeout(() => {
                const newItem = JSON.parse(JSON.stringify(this.form));

                if (isBackup) {
                  newItem.wmsTaskId = this.nextBackupId++;
                  this.mockBackupData.unshift(newItem);
                } else {
                  newItem.wmsTaskId = this.nextId++;
                  this.mockData.unshift(newItem);
                }

                newItem.createTime = new Date().toLocaleString();

                this.$modal.msgSuccess(isBackup ? "新增备份任务成功" : "新增主任务成功");
                this.open = false;
                this.getList();
              }, 300);
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const wmsTaskIds = row.wmsTaskId || this.ids;
      const isBackup = row.level === 2; // 通过level判断是否为备份任务
      const apiMethod = isBackup ? delBak : delTask;
      const confirmMsg = isBackup ? '是否确认删除备份任务编号为"' + wmsTaskIds + '"的数据项？' :
                                   '是否确认删除WMS任务编号为"' + wmsTaskIds + '"的数据项？';

      this.$modal.confirm(confirmMsg).then(function () {
        // 尝试调用后端API删除
        return apiMethod(wmsTaskIds);
      }).then(() => {
        // 删除成功后直接刷新整个列表
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(error => {
        // 判断是否是用户取消操作
        if (error === 'cancel') {
          return;
        }

        console.log("后端API调用失败，使用模拟数据", error);

        // 使用模拟数据作为后备方案
        setTimeout(() => {
          if (isBackup) {
            console.log("正在删除备份任务", wmsTaskIds);
            if (Array.isArray(wmsTaskIds)) {
              this.mockBackupData = this.mockBackupData.filter(item => !wmsTaskIds.includes(item.wmsTaskId));
            } else {
              this.mockBackupData = this.mockBackupData.filter(item => item.wmsTaskId !== wmsTaskIds);
            }
            
            // 对于备份任务，直接从当前树形数据中移除
            if (row.parentNode && row.parentNode.data) {
              // 如果有父节点信息，直接从父节点的children中移除
              const parentData = row.parentNode.data;
              if (parentData.children && Array.isArray(parentData.children)) {
                parentData.children = parentData.children.filter(item => item.wmsTaskId !== row.wmsTaskId);
                // 如果children为空，则移除children属性
                if (parentData.children.length === 0) {
                  delete parentData.children;
                }
              }
            } else {
              // 如果没有父节点信息，则完全刷新列表
              this.getList();
            }
          } else {
            if (Array.isArray(wmsTaskIds)) {
              this.mockData = this.mockData.filter(item => !wmsTaskIds.includes(item.wmsTaskId));
            } else {
              this.mockData = this.mockData.filter(item => item.wmsTaskId !== wmsTaskIds);
            }
            // 主任务直接刷新整个列表
            this.getList();
          }

          this.$modal.msgSuccess("删除成功");
        }, 300);
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      try {
        this.download('system/task/export', {
          ...this.queryParams
        }, `task_${new Date().getTime()}.xlsx`);
      } catch (error) {
        console.log("导出功能调用失败，显示模拟消息", error);
        this.$modal.msgSuccess("模拟导出功能：已导出到文件 task_" + new Date().getTime() + ".xlsx");
      }
    },
    /** 获取备份任务数据 */
    getBackupTasks() {
      // 调用备份任务API获取所有备份任务
      return new Promise((resolve, reject) => {
        listBak({}).then(response => {
          console.log('获取到所有备份任务数据:', response.rows);
          const backupTasks = response.rows || [];

          // 处理备份任务数据
          const processedTasks = backupTasks.map(task => {
            return {
              ...task,
              level: 2, // 标记为第二层级
            };
          });

          resolve(processedTasks);
        }).catch(error => {
          console.log("获取备份任务失败，使用模拟数据", error);

          // 使用模拟数据作为后备方案
          setTimeout(() => {
            // 返回所有模拟备份任务
            const processedTasks = this.mockBackupData.map(task => {
              return {
                ...task,
                level: 2, // 标记为第二层级
              };
            });

            resolve(processedTasks);
          }, 300);
        });
      });
    },
    /** 表格行样式 */
    tableRowClassName({row, rowIndex}) {
      if (row.level === 2) {
        return 'backup-row';
      }
      return '';
    },
    /** 表格行点击操作 */
    handleRowClick(row) {
      // 实现表格行点击后的逻辑
      console.log("表格行点击操作", row);
    },
    /** 删除备份任务操作，确保直接删除树形结构 */
    handleBackupDelete(row) {
      const wmsTaskId = row.wmsTaskId;
      const confirmMsg = '是否确认删除备份任务编号为"' + wmsTaskId + '"的数据项？';
      
      // 查找主任务和对应的备份任务索引
      const findParentTaskAndChildIndex = () => {
        for (let i = 0; i < this.taskList.length; i++) {
          const task = this.taskList[i];
          if (task.children && Array.isArray(task.children)) {
            const childIndex = task.children.findIndex(child => child.wmsTaskId === wmsTaskId);
            if (childIndex !== -1) {
              return { parentTask: task, childIndex };
            }
          }
        }
        return null;
      };
      
      this.$modal.confirm(confirmMsg).then(function () {
        // 尝试调用后端API删除
        return delBak(wmsTaskId);
      }).then(() => {
        // 从树形结构中直接删除
        const result = findParentTaskAndChildIndex();
        if (result) {
          result.parentTask.children.splice(result.childIndex, 1);
          if (result.parentTask.children.length === 0) {
            // 如果子节点为空，删除children属性
            this.$set(result.parentTask, 'children', null);
          }
          this.$modal.msgSuccess("删除成功");
        } else {
          // 如果找不到对应结构，刷新整个列表
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }
      }).catch(error => {
        // 判断是否是用户取消操作
        if (error === 'cancel') {
          return;
        }
        
        console.log("后端API调用失败，尝试前端删除", error);
        
        // 前端直接移除
        const result = findParentTaskAndChildIndex();
        if (result) {
          result.parentTask.children.splice(result.childIndex, 1);
          if (result.parentTask.children.length === 0) {
            this.$set(result.parentTask, 'children', null);
          }
          // 同时更新模拟数据
          this.mockBackupData = this.mockBackupData.filter(item => item.wmsTaskId !== wmsTaskId);
          this.$modal.msgSuccess("删除成功");
        } else {
          // 找不到结构，刷新整个列表
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }
      });
    },
  }
};
</script>

<style scoped>
.chart-container {
  padding: 20px;
}
.chart-wrapper {
  padding: 20px;
  height: 300px;
}
.box-card {
  margin-bottom: 20px;
}

/* 树形表格的样式 */
/deep/ .el-table__expand-icon {
  margin-right: 8px;
}

/* 备份任务行样式 */
/deep/ .backup-row {
  background-color: #fff7e6; /* 柔和的浅橙色背景 */
  color: #606266;
  font-size: 13px;
  border-left: 3px solid #ffd591;
}

/* 行悬停高亮 */
/deep/ .el-table__body tr:hover>td {
  background: #f5f7fa !important;
}

/* 表头样式 */
/deep/ .el-table thead {
  background: #fafafa;
  color: #303133;
  font-weight: 600;
}

/* 表格单元格样式 */
/deep/ .el-table td, /deep/ .el-table th {
  padding: 8px 0;
}

/* 表格行样式 */
/deep/ .el-table .el-table__row {
  height: auto;
  min-height: 40px;
}

/* 紧凑表格，减少多余空间 */
/deep/ .el-table--border {
  border-radius: 4px;
  overflow: hidden;
  box-shadow: none;
  max-width: 100%;
}

/* 调整展开图标样式 */
/deep/ .el-table__expand-icon {
  margin-right: 8px;
  color: #409EFF;
}

/* 表格视图容器样式 */
.table-container {
  padding: 0;
  width: 100%;
  overflow: hidden;
}

/* 表格宽度控制 */
/deep/ .el-table {
  width: 100% !important;
  margin: 0;
}

/* app容器去除多余填充 */
.app-container {
  background: #f0f2f5;
  padding: 6px;
  width: 100%;
  box-sizing: border-box;
  margin: 0;
}

/* 添加卡片样式，减少内边距 */
.app-container > div {
  background: white;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  padding: 12px;
  margin-bottom: 10px;
  width: 100%;
}

/* 搜索区域样式 */
.search-container {
  margin-bottom: 16px;
}

/* 操作区域样式 */
.action-container {
  margin-bottom: 0;
  padding-bottom: 8px;
}

/* 分页容器样式 */
.pagination-container {
  padding: 16px 0 0 0;
  margin-top: 0;
  background: white;
}

/* 标签样式 */
.el-tag {
  margin-right: 5px;
}

/* 分页器样式 */
.pagination-container {
  margin-top: 15px;
}

/* 操作按钮样式 */
/deep/ .el-button--text {
  padding: 4px 8px;
  font-size: 14px;
}

/deep/ .el-button--text:hover {
  color: #409EFF;
  background-color: #ecf5ff;
  border-radius: 4px;
}

/* 标签样式优化 */
/deep/ .el-tag {
  margin-right: 4px;
  border-radius: 3px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

/* 主任务标签 */
/deep/ .el-tag--success {
  background-color: #f0f9eb;
  border-color: #e1f3d8;
}

/* 备份任务标签 */
/deep/ .el-tag--warning {
  background-color: #fdf6ec;
  border-color: #faecd8;
}

/* 页面外边距减少 */
body {
  margin: 0;
  padding: 0;
}

/* 适应容器宽度 */
/deep/ .el-table__header, 
/deep/ .el-table__body, 
/deep/ .el-table__footer {
  width: 100% !important;
}
</style>
