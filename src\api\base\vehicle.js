import request from '@/utils/request'

// 查询AGV小车表列表
export function listVehicle(query) {
  return request({
    url: '/base/vehicle/list',
    method: 'get',
    params: query
  })
}

// 查询AGV小车表详细
export function getVehicle(vehicleId) {
  return request({
    url: '/base/vehicle/' + vehicleId,
    method: 'get'
  })
}

// 新增AGV小车表
export function addVehicle(data) {
  return request({
    url: '/base/vehicle',
    method: 'post',
    data: data
  })
}

// 修改AGV小车表
export function updateVehicle(data) {
  return request({
    url: '/base/vehicle',
    method: 'put',
    data: data
  })
}

// 删除AGV小车表
export function delVehicle(vehicleId) {
  return request({
    url: '/base/vehicle/' + vehicleId,
    method: 'delete'
  })
}
