<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="异常类型" prop="errorType">
        <el-select 
          v-model="queryParams.errorType" 
          placeholder="请选择异常类型" 
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.error_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="异常等级" prop="errorLevel">
        <el-select 
          v-model="queryParams.errorLevel" 
          placeholder="请选择异常等级" 
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.error_level"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="车辆编号" prop="vehicleCode">
        <el-input
          v-model="queryParams.vehicleCode"
          placeholder="请输入关联车辆编号"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="处理状态" prop="handleStatus">
        <el-select 
          v-model="queryParams.handleStatus" 
          placeholder="请选择处理状态" 
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.handle_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="操作时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['log:errorLog:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleClean"
          v-hasPermi="['log:errorLog:remove']"
        >清空</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['log:errorLog:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="tables" v-loading="loading" :data="list" @selection-change="handleSelectionChange" :default-sort="defaultSort" @sort-change="handleSortChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="日志编号" align="center" prop="id" width="120"/>
      <el-table-column label="异常编号" align="center" prop="errorNo"  :show-overflow-tooltip="true"/>
      <el-table-column label="异常类型" align="center" prop="errorType" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.error_type" :value="scope.row.errorType"/>
        </template>
      </el-table-column>
      <el-table-column label="异常等级" align="center" prop="errorLevel" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.error_level" :value="scope.row.errorLevel"/>
        </template>
      </el-table-column>
      <el-table-column label="车辆编号" align="center" prop="vehicleCode" width="120" :show-overflow-tooltip="true"/>
      <el-table-column label="异常代码" align="center" prop="errorCode" width="120" :show-overflow-tooltip="true"/>
      <el-table-column label="异常描述" align="center" prop="errorDesc" :show-overflow-tooltip="true"/>
      <el-table-column label="处理状态" align="center" prop="handleStatus" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.handle_status" :value="scope.row.handleStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="处理人" align="center" prop="handleBy" width="120" :show-overflow-tooltip="true"/>
      <el-table-column label="发生时间" align="center" prop="errorTime" width="180" sortable="custom" :sort-orders="['descending', 'ascending']">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.errorTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row,scope.index)"
            v-hasPermi="['log:errorLog:query']"
          >详细</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 异常日志详细 -->
    <el-dialog title="异常日志详细" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px" size="mini">
        <el-row>
          <el-col :span="12">
            <el-form-item label="异常编号：">{{ form.errorNo }}</el-form-item>
            <el-form-item label="异常类型：">
              <dict-tag :options="dict.type.error_type" :value="form.errorType"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="异常等级：">
              <dict-tag :options="dict.type.error_level" :value="form.errorLevel"/>
            </el-form-item>
            <el-form-item label="车辆编号：">{{ form.vehicleCode }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="异常位置：">{{ form.errorPosition }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="异常描述：">{{ form.errorDesc }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="异常堆栈：">{{ form.errorStack }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="处理方案：">{{ form.handleMethod }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注：">{{ form.remark }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理状态：">
              <dict-tag :options="dict.type.handle_status" :value="form.handleStatus"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理人：">{{ form.handleBy }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="处理时间：">{{ parseTime(form.handleTime) }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listErrorLog, delErrorLog, cleanErrorLog } from "@/api/log/errorLog";

export default {
  name: "ErrorLog",
  dicts: ['error_type', 'error_level', 'handle_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 默认排序
      defaultSort: {prop: 'errorTime', order: 'descending'},
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        errorType: undefined,
        errorLevel: undefined,
        vehicleCode: undefined,
        handleStatus: undefined
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询异常日志列表 */
    getList() {
      this.loading = true;
      listErrorLog(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.list = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.pageNum = 1;
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    /** 排序触发事件 */
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true;
      this.form = row;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除日志编号为"' + ids + '"的数据项？').then(function() {
        return delErrorLog(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 清空按钮操作 */
    handleClean() {
      this.$modal.confirm('���否确认清空所有异常日志数据项？').then(function() {
        return cleanErrorLog();
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("清空成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('log/errorLog/export', {
        ...this.queryParams
      }, `异常日志_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
