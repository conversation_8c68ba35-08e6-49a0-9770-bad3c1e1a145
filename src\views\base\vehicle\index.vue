<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="小车编码" prop="vehicleCode">
        <el-input
          v-model="queryParams.vehicleCode"
          placeholder="请输入小车编码"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="小车名称" prop="vehicleName">
        <el-input
          v-model="queryParams.vehicleName"
          placeholder="请输入小车名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="小车类型" prop="vehicleType">
        <el-select v-model="queryParams.vehicleType" placeholder="请选择小车类型" clearable>
          <el-option
            v-for="dict in dict.type.vehicle_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="小车状态" prop="vehicleStatus">
        <el-select v-model="queryParams.vehicleStatus" placeholder="请选择小车状态" clearable>
          <el-option
            v-for="dict in dict.type.vehicle_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="载货状态" prop="loadStatus">
        <el-select v-model="queryParams.loadStatus" placeholder="请选择载货状态" clearable>
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini"
          @click="handleAdd"
          v-hasPermi="['base:vehicle:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['base:vehicle:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['base:vehicle:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini"
          @click="handleExport"
          v-hasPermi="['base:vehicle:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="vehicleList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="小车编号" align="center" prop="vehicleId" />
      <el-table-column label="小车编码" align="center" prop="vehicleCode" />
      <el-table-column label="小车名称" align="center" prop="vehicleName" />
      <el-table-column label="小车类型" align="center" prop="vehicleType">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.vehicle_type" :value="scope.row.vehicleType" />
        </template>
      </el-table-column>
      <el-table-column label="小车状态" align="center" prop="vehicleStatus">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.vehicle_status" :value="scope.row.vehicleStatus" />
        </template>
      </el-table-column>
      <el-table-column label="当前位置" align="center" prop="currentPosition" />
      <el-table-column label="当前速度" align="center" prop="currentSpeed" />
      <el-table-column label="电池电量" align="center" prop="batteryLevel" />
      <el-table-column label="载货状态" align="center" prop="loadStatus">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.loadStatus" />
        </template>
      </el-table-column>
      <el-table-column label="错误代码" align="center" prop="errorCode" />
      <el-table-column label="错误信息" align="center" prop="errorMessage" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['base:vehicle:edit']"
          >修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['base:vehicle:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改AGV小车表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="小车编码" prop="vehicleCode">
          <el-input v-model="form.vehicleCode" placeholder="请输入小车编码" />
        </el-form-item>
        <el-form-item label="小车名称" prop="vehicleName">
          <el-input v-model="form.vehicleName" placeholder="请输入小车名称" />
        </el-form-item>
        <el-form-item label="小车类型" prop="vehicleType">
          <el-select v-model="form.vehicleType" placeholder="请选择小车类型">
            <el-option
              v-for="dict in dict.type.vehicle_type"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="小车状态" prop="vehicleStatus">
          <el-select v-model="form.vehicleStatus" placeholder="请选择小车状态">
            <el-option
              v-for="dict in dict.type.vehicle_status"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="当前位置" prop="currentPosition">
          <el-input v-model="form.currentPosition" placeholder="请输入当前位置" />
        </el-form-item>
        <el-form-item label="当前速度" prop="currentSpeed">
          <el-input v-model="form.currentSpeed" placeholder="请输入当前速度" />
        </el-form-item>
        <el-form-item label="电池电量" prop="batteryLevel">
          <el-input v-model="form.batteryLevel" placeholder="请输入电池电量" />
        </el-form-item>
        <el-form-item label="载货状态" prop="loadStatus">
          <el-radio-group v-model="form.loadStatus">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="错误代码" prop="errorCode">
          <el-input v-model="form.errorCode" placeholder="请输入错误代码" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-input v-model="form.delFlag" placeholder="请输入删除标志" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listVehicle, getVehicle, delVehicle, addVehicle, updateVehicle } from "@/api/base/vehicle";

export default {
  name: "Vehicle",
  dicts: ['vehicle_type', 'vehicle_status', 'sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // AGV小车表表格数据
      vehicleList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        vehicleCode: null,
        vehicleName: null,
        vehicleType: null,
        vehicleStatus: null,
        loadStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        vehicleCode: [
          { required: true, message: "小车编码不能为空", trigger: "blur" }
        ],
        vehicleName: [
          { required: true, message: "小车名称不能为空", trigger: "blur" }
        ],
        vehicleType: [
          { required: true, message: "小车类型不能为空", trigger: "change" }
        ],
        vehicleStatus: [
          { required: true, message: "小车状态不能为空", trigger: "change" }
        ],
        loadStatus: [
          { required: true, message: "载货状态不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询AGV小车表列表 */
    getList() {
      this.loading = true;
      listVehicle(this.queryParams).then(response => {
        this.vehicleList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        vehicleId: null,
        vehicleCode: null,
        vehicleName: null,
        vehicleType: null,
        vehicleStatus: null,
        currentPosition: null,
        currentSpeed: null,
        batteryLevel: null,
        loadStatus: null,
        errorCode: null,
        errorMessage: null,
        delFlag: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.vehicleId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加AGV小车表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const vehicleId = row.vehicleId || this.ids;
      getVehicle(vehicleId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改AGV小车表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.vehicleId != null) {
            updateVehicle(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addVehicle(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const vehicleIds = row.vehicleId || this.ids;
      this.$modal.confirm('是否确认删除@Model.FunctionName)编号为"' + vehicleIds + '"的数据项？').then(function() {
        return delVehicle(vehicleIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('base/vehicle/export', {
        ...this.queryParams
      }, `vehicle_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
