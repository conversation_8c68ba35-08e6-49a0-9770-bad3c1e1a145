<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="升降机编号" prop="elevatorCode" label-width="90px">
        <el-input v-model="queryParams.elevatorCode" placeholder="请输入升降机编号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="升降机类型" prop="elevatorType" label-width="90px">
        <el-select v-model="queryParams.elevatorType" placeholder="请选择升降机类型" clearable>
          <el-option v-for="dict in dict.type.elevator_type" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="升降机状态" prop="elevatorStatus" label-width="90px">
        <el-select v-model="queryParams.elevatorStatus" placeholder="请选择升降机状态" clearable>
          <el-option v-for="dict in dict.type.elevator_status" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="门状态" prop="doorStatus">
        <el-select v-model="queryParams.doorStatus" placeholder="请选择门状态" clearable>
          <el-option v-for="dict in dict.type.door_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="载货状态" prop="loadStatus">
        <el-select v-model="queryParams.loadStatus" placeholder="请选择载货状态" clearable>
          <el-option v-for="dict in dict.type.load_status" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="错误代码" prop="errorCode">
        <el-input v-model="queryParams.errorCode" placeholder="请输入错误代码" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="错误信息" prop="errorMessage">
        <el-input v-model="queryParams.errorMessage" placeholder="请输入错误信息" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['base:elevator:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['base:elevator:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['base:elevator:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['base:elevator:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="elevatorList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="升降机ID" align="center" prop="elevatorId" />
      <el-table-column label="升降机编号" align="center" prop="elevatorCode" />
      <el-table-column label="升降机名称" align="center" prop="elevatorName" />
      <el-table-column label="升降机类型" align="center" prop="elevatorType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.elevator_type" :value="scope.row.elevatorType" />
        </template>
      </el-table-column>
      <el-table-column label="升降机状态" align="center" prop="elevatorStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.elevator_status" :value="scope.row.elevatorStatus" />
        </template>
      </el-table-column>
      <el-table-column label="当前楼层" align="center" prop="currentFloor" />
      <el-table-column label="目标楼层" align="center" prop="targetFloor" />
      <el-table-column label="门状态" align="center" prop="doorStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.door_status" :value="scope.row.doorStatus" />
        </template>
      </el-table-column>
      <el-table-column label="载货状态" align="center" prop="loadStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.load_status" :value="scope.row.loadStatus" />
        </template>
      </el-table-column>
      <el-table-column label="错误代码" align="center" prop="errorCode" />
      <el-table-column label="错误信息" align="center" prop="errorMessage" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['base:elevator:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['base:elevator:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改升降机表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="升降机编号" prop="elevatorCode">
          <el-input v-model="form.elevatorCode" placeholder="请输入升降机编号" />
        </el-form-item>
        <el-form-item label="升降机名称" prop="elevatorName">
          <el-input v-model="form.elevatorName" placeholder="请输入升降机名称" />
        </el-form-item>
        <el-form-item label="升降机类型" prop="elevatorType">
          <el-select v-model="form.elevatorType" placeholder="请选择升降机类型">
            <el-option v-for="dict in dict.type.elevator_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="升降机状态" prop="elevatorStatus">
          <el-select v-model="form.elevatorStatus" placeholder="请选择升降机状态">
            <el-option v-for="dict in dict.type.elevator_status" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="当前楼层" prop="currentFloor">
          <el-input v-model="form.currentFloor" placeholder="请输入当前楼层" />
        </el-form-item>
        <el-form-item label="目标楼层" prop="targetFloor">
          <el-input v-model="form.targetFloor" placeholder="请输入目标楼层" />
        </el-form-item>
        <el-form-item label="门状态" prop="doorStatus">
          <el-select v-model="form.doorStatus" placeholder="请选择门状态">
            <el-option v-for="dict in dict.type.door_status" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="载货状态" prop="loadStatus">
          <el-radio-group v-model="form.loadStatus">
            <el-radio v-for="dict in dict.type.load_status" :key="dict.value" :label="dict.value">{{ dict.label
              }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="错误代码" prop="errorCode">
          <el-input v-model="form.errorCode" placeholder="请输入错误代码" />
        </el-form-item>
        <el-form-item label="错误信息" prop="errorMessage">
          <el-input v-model="form.errorMessage" placeholder="请输入错误信息" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listElevator, getElevator, delElevator, addElevator, updateElevator } from "@/api/base/elevator";

export default {
  name: "Elevator",
  dicts: ['elevator_type', 'elevator_status', 'door_status', 'load_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 升降机表表格数据
      elevatorList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        elevatorCode: null,
        elevatorType: null,
        elevatorStatus: null,
        doorStatus: null,
        loadStatus: null,
        errorCode: null,
        errorMessage: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        elevatorCode: [
          { required: true, message: "升降机编号不能为空", trigger: "blur" }
        ],
        elevatorType: [
          { required: true, message: "升降机类型不能为空", trigger: "change" }
        ],
        elevatorStatus: [
          { required: true, message: "升降机状态不能为空", trigger: "change" }
        ],
        doorStatus: [
          { required: true, message: "门状态不能为空", trigger: "change" }
        ],
        loadStatus: [
          { required: true, message: "载货状态不能为空", trigger: "change" }
        ],
        // errorCode: [
        //   { required: true, message: "错误代码不能为空", trigger: blur }
        // ],
        // errorMessage: [
        //   { required: true, message: "错误信息不能为空", trigger: blur }
        // ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询升降机表列表 */
    getList() {
      this.loading = true;
      listElevator(this.queryParams).then(response => {
        this.elevatorList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        elevatorId: null,
        elevatorCode: null,
        elevatorName: null,
        elevatorType: null,
        elevatorStatus: null,
        currentFloor: null,
        targetFloor: null,
        doorStatus: null,
        loadStatus: null,
        errorCode: null,
        errorMessage: null,
        delFlag: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.elevatorId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加升降机表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const elevatorId = row.elevatorId || this.ids;
      getElevator(elevatorId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改升降机表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.elevatorId != null) {
            updateElevator(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addElevator(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const elevatorIds = row.elevatorId || this.ids;
      this.$modal.confirm('是否确认删除@Model.FunctionName)编号为"' + elevatorIds + '"的数据项？').then(function () {
        return delElevator(elevatorIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('base/elevator/export', {
        ...this.queryParams
      }, `elevator_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
