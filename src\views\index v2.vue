<template>
  <div class="app-container home">
    <el-row :gutter="20">
      <el-col :sm="24" :lg="12" style="padding-left: 20px">
        <h2>WCS控制系统</h2>
        <p>
          <a href="https://gitee.com/y_project/RuoYi" target="_blank">[若依]</a>是一款优秀的Java平台下的后台管理系统, 非常受欢迎. .NET平台下也有很多类似的开源项目, 但使用起来总感觉不够顺手, 因此便有了RuoYi.Net项目. RuoYi.Net目前仅实现了前后端分离版, 后端几乎一比一复刻了RuoYi后端功能, 前端仅对原ruoyi-ui(vue2版本)做了极小的改动以适应.net(服务监控页面的java相关信息改成了.net相关信息, 不在意此功能的可直接使用原ruoyi-ui).
        </p>
        <p>
          <b>当前版本:</b> <span>v{{ version }}</span>
        </p>
        <p>
          <el-tag type="danger">&yen;免费开源</el-tag>
        </p>
        <p>
          <el-button
            type="primary"
            size="mini"
            icon="el-icon-cloudy"
            plain
            @click="goTarget('https://gitee.com/wdyday/ruoyi.net')"
            >访问码云</el-button
          >
        </p>
      </el-col>

      <el-col :sm="24" :lg="12" style="padding-left: 50px">
        <el-row>
          <el-col :span="12">
            <h2>技术选型</h2>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="6">
            <h4>后端技术</h4>
            <ul>
              <li>.NET 8</li>
              <li>JWT</li>
              <li>SqlSugar</li>
              <li>MiniExcel</li>
              <li>Quartz.Net</li>
              <li>...</li>
            </ul>
          </el-col>
          <el-col :span="6">
            <h4>前端技术</h4>
            <ul>
              <li>Vue</li>
              <li>Vuex</li>
              <li>Element-ui</li>
              <li>Axios</li>
              <li>Sass</li>
              <li>Quill</li>
              <li>...</li>
            </ul>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-divider />
    <el-row :gutter="20">
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="update-log">
          <div slot="header" class="clearfix">
            <span>联系信息</span>
          </div>
          <div class="body">
            <p>
              <i class="el-icon-s-promotion"></i> 官网：<el-link
                href="#"
                target="_blank"
                >暂无</el-link
              >
            </p>
            <p>
              <i class="el-icon-user-solid"></i> QQ群：<a href="#" target="_blank">暂无</a>
            </p>
            <!-- <p>
              <i class="el-icon-chat-dot-round"></i> 微信：<a
                href="javascript:;"
                >/ *若依.NET</a
              >
            </p>
            <p>
              <i class="el-icon-money"></i> 支付宝：<a
                href="javascript:;"
                class="支付宝信息"
                >/ *若依.NET</a
              >
            </p> -->
          </div>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="update-log">
          <div slot="header" class="clearfix">
            <span>更新日志</span>
          </div>
          <el-collapse accordion>
            <el-collapse-item title="v1.0.0 - 2023-10-20">
              <ol>
                <li>若依.NET前后端分离系统正式发布</li>
              </ol>
            </el-collapse-item>
            <el-collapse-item title="v1.0.1 - 2023-10-31">
              <ol>
                <li>支持 SqlServer & 修复bug</li>
              </ol>
            </el-collapse-item>
            <el-collapse-item title="v1.0.2 - 2023-11-07">
              <ol>
                <li>添加限流 & 修复模板判断条件bug</li>
              </ol>
            </el-collapse-item>
            <el-collapse-item title="v2.0.0 - 2024-02-21">
              <ol>
                <li>移除Furion引用</li>
              </ol>
            </el-collapse-item>
            <el-collapse-item title="v2.0.1 - 2024-08-28">
              <ol>
                <li>升级到.NET 8</li>
              </ol>
            </el-collapse-item>
          </el-collapse>
        </el-card>
      </el-col>
      <el-col :xs="24" :sm="24" :md="12" :lg="8">
        <el-card class="update-log">
          <div slot="header" class="clearfix">
            <span>捐赠支持</span>
          </div>
          <div class="body">
            <img
              src="@/assets/images/pay.png"
              alt="donate"
              width="100%"
            />
            <span style="display: inline-block; height: 30px; line-height: 30px"
              >你可以请作者喝杯咖啡表示鼓励</span
            >
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "Index",
  data() {
    return {
      // 版本号
      version: "1.0.1"
    };
  },
  methods: {
    goTarget(href) {
      window.open(href, "_blank");
    }
  }
};
</script>

<style scoped lang="scss">
.home {
  blockquote {
    padding: 10px 20px;
    margin: 0 0 20px;
    font-size: 17.5px;
    border-left: 5px solid #eee;
  }
  hr {
    margin-top: 20px;
    margin-bottom: 20px;
    border: 0;
    border-top: 1px solid #eee;
  }
  .col-item {
    margin-bottom: 20px;
  }

  ul {
    padding: 0;
    margin: 0;
  }

  font-family: "open sans", "Helvetica Neue", Helvetica, Arial, sans-serif;
  font-size: 13px;
  color: #676a6c;
  overflow-x: hidden;

  ul {
    list-style-type: none;
  }

  h4 {
    margin-top: 0px;
  }

  h2 {
    margin-top: 10px;
    font-size: 26px;
    font-weight: 100;
  }

  p {
    margin-top: 10px;

    b {
      font-weight: 700;
    }
  }

  .update-log {
    ol {
      display: block;
      list-style-type: decimal;
      margin-block-start: 1em;
      margin-block-end: 1em;
      margin-inline-start: 0;
      margin-inline-end: 0;
      padding-inline-start: 40px;
    }
  }
}
</style>

