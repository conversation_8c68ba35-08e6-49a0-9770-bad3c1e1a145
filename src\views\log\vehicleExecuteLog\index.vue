<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务编号" prop="taskId">
        <el-input
          v-model="queryParams.taskId"
          placeholder="请输入任务编号"
          clearable
          style="width: 240px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="车辆编号" prop="vehicleCode">
        <el-input
          v-model="queryParams.vehicleCode"
          placeholder="请输入车辆编号"
          clearable
          style="width: 240px;"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="动作类型" prop="actionType">
        <el-select 
          v-model="queryParams.actionType" 
          placeholder="请选择动作类型" 
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.vehicle_action_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="执行状态" prop="executeStatus">
        <el-select 
          v-model="queryParams.executeStatus" 
          placeholder="请选择执行状态" 
          clearable
          style="width: 240px"
        >
          <el-option
            v-for="dict in dict.type.vehicle_execute_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="操作时间">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd HH:mm:ss"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :default-time="['00:00:00', '23:59:59']"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['log:vehicleExecuteLog:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          @click="handleClean"
          v-hasPermi="['log:vehicleExecuteLog:remove']"
        >清空</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['log:vehicleExecuteLog:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table ref="tables" v-loading="loading" :data="list" @selection-change="handleSelectionChange" :default-sort="defaultSort" @sort-change="handleSortChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="日志编号" align="center" prop="id" v-if="false"/>
      <el-table-column label="任务编号" align="center" prop="taskId" width="220"/>
      <el-table-column label="车辆编号" align="center" prop="vehicleCode" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="动作类型" align="center" prop="actionType" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.vehicle_action_type" :value="scope.row.actionType"/>
        </template>
      </el-table-column>
      <el-table-column label="行驶方向" align="center" prop="direction" width="120">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.vehicle_direction_type" :value="scope.row.direction"/>
        </template>
      </el-table-column>
      <el-table-column label="起始位置" align="center" prop="startPos" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="目标位置" align="center" prop="targetPos" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="当前位置" align="center" prop="currentPos" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="执行状态" align="center" prop="executeStatus" width="100">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.vehicle_execute_status" :value="scope.row.executeStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="开始时间" align="center" prop="startTime" width="180" sortable="custom" :sort-orders="['descending', 'ascending']">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="耗时" align="center" prop="duration" width="110" :show-overflow-tooltip="true" sortable="custom" :sort-orders="['descending', 'ascending']">
        <template slot-scope="scope">
          <span>{{ scope.row.duration }}秒</span>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row,scope.index)"
            v-hasPermi="['log:vehicleExecuteLog:query']"
          >详细</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 执行日志详细 -->
    <el-dialog title="执行日志详细" :visible.sync="open" width="700px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px" size="mini">
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务编号：">{{ form.taskId }}</el-form-item>
            <el-form-item label="车辆编号：">{{ form.vehicleCode }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="动作类型：">
              <dict-tag :options="dict.type.vehicle_action_type" :value="form.actionType"/>
            </el-form-item>
            <el-form-item label="行驶方向：">
              <dict-tag :options="dict.type.vehicle_direction_type" :value="form.direction"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="起始位置：">{{ form.startPos }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标位置：">{{ form.targetPos }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="当前位置：">{{ form.currentPos }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始时间：">{{ parseTime(form.startTime) }}</el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间：">{{ parseTime(form.endTime) }}</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="执行状态：">
              <dict-tag :options="dict.type.vehicle_execute_status" :value="form.executeStatus"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="执行耗时：">{{ form.duration }}秒</el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="执行速度：">{{ form.speed }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="备注：">{{ form.remark }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="错误代码："  v-if="form.executeStatus === '3'">{{ form.errorCode }}</el-form-item>
          </el-col>          
          <el-col :span="24">
            <el-form-item label="错误信息：" v-if="form.executeStatus === '3'">{{ form.errorMsg }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listVehicleExecuteLog, delVehicleExecuteLog, cleanVehicleExecuteLog } from "@/api/log/vehicleExecuteLog";

export default {
  name: "VehicleExecuteLog",
  dicts: ['vehicle_action_type', 'vehicle_direction_type', 'vehicle_execute_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 表格数据
      list: [],
      // 是否显示弹出层
      open: false,
      // 日期范围
      dateRange: [],
      // 默认排序
      defaultSort: {prop: 'startTime', order: 'descending'},
      // 表单参数
      form: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        taskId: undefined,
        vehicleCode: undefined,
        actionType: undefined,
        executeStatus: undefined
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询执行日志列表 */
    getList() {
      this.loading = true;
      listVehicleExecuteLog(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
          this.list = response.rows;
          this.total = response.total;
          this.loading = false;
        }
      );
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.queryParams.pageNum = 1;
      this.$refs.tables.sort(this.defaultSort.prop, this.defaultSort.order)
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.multiple = !selection.length
    },
    /** 排序触发事件 */
    handleSortChange(column, prop, order) {
      this.queryParams.orderByColumn = column.prop;
      this.queryParams.isAsc = column.order;
      this.getList();
    },
    /** 详细按钮操作 */
    handleView(row) {
      this.open = true;
      this.form = row;
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除日志编号为"' + ids + '"的数据项？').then(function() {
        return delVehicleExecuteLog(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 清空按钮操作 */
    handleClean() {
      this.$modal.confirm('是否确认清空所有执行日志数据项？').then(function() {
        return cleanVehicleExecuteLog();
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("清空成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('log/vehicleExecuteLog/export', {
        ...this.queryParams
      }, `四向车执行日志_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
