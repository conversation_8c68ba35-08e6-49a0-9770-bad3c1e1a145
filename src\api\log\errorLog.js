import request from '@/utils/request'

// 查询异常日志表列表
export function listErrorLog(query) {
  return request({
    url: '/log/errorLog/list',
    method: 'get',
    params: query
  })
}

// 查询异常日志表详细
export function getErrorLog(id) {
  return request({
    url: '/log/errorLog/' + id,
    method: 'get'
  })
}

// 新增异常日志表
export function addErrorLog(data) {
  return request({
    url: '/log/errorLog',
    method: 'post',
    data: data
  })
}

// 修改异常日志表
export function updateErrorLog(data) {
  return request({
    url: '/log/errorLog',
    method: 'put',
    data: data
  })
}

// 删除异常日志表
export function delErrorLog(id) {
  return request({
    url: '/log/errorLog/' + id,
    method: 'delete'
  })
}

// 清空操作日志
export function cleanErrorLog() {
  return request({
    url: '/log/errorLog/clean',
    method: 'delete'
  })
}