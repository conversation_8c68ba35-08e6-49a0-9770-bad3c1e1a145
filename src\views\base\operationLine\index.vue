<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="运行编号" prop="operationCode">
        <el-input v-model="queryParams.operationCode" placeholder="请输入运行编号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="线路ID" prop="lineId">
        <el-input v-model="queryParams.lineId" placeholder="请输入线路ID" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="AGV" prop="vehicleId">
        <el-input v-model="queryParams.vehicleId" placeholder="请输入AGV编号" clearable @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="提升机" prop="elevatorId">
        <el-input v-model=" queryParams.elevatorId" placeholder="请输入提升机编号" clearable
          @keyup.enter.native="handleQuery" />
      </el-form-item>
      <el-form-item label="开始时间" prop="startTime">
        <el-date-picker clearable v-model="queryParams.startTime" type="date" value-format="yyyy-MM-dd"
          placeholder="请选择开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="结束时间" prop="endTime">
        <el-date-picker clearable v-model="queryParams.endTime" type="date" value-format="yyyy-MM-dd"
          placeholder="请选择结束时间">
        </el-date-picker>
      </el-form-item>
      <!-- <el-form-item label="运行状态" prop="status">
        <el-input
          v-model="queryParams.status"
          placeholder="请输入运行状态"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <!-- <el-form-item label="实际速度" prop="actualSpeed">
        <el-input
          v-model="queryParams.actualSpeed"
          placeholder="请输入实际速度"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="运行方向" prop="direction">
        <el-input
          v-model="queryParams.direction"
          placeholder="请输入运行方向"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <!-- <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['base:operationLine:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['base:operationLine:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['base:operationLine:remove']">删除</el-button>
      </el-col> -->
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['base:operationLine:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="operationLineList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="运行ID" align="center" prop="operationId" />
      <el-table-column label="运行编号" align="center" prop="operationCode" />
      <el-table-column label="线路ID" align="center" prop="lineId" />
      <el-table-column label="AGV ID" align="center" prop="vehicleId" />
      <el-table-column label="升降机ID" align="center" prop="elevatorId" />
      <el-table-column label="开始时间" align="center" prop="startTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.startTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="结束时间" align="center" prop="endTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.endTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="运行状态" align="center" prop="status" />
      <el-table-column label="实际速度" align="center" prop="actualSpeed" />
      <el-table-column label="运行方向" align="center" prop="direction" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <!-- <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['base:operationLine:edit']">修改</el-button> -->
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['base:operationLine:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改运行线路表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="运行编号" prop="operationCode">
          <el-input v-model="form.operationCode" placeholder="请输入运行编号" />
        </el-form-item>
        <el-form-item label="线路ID" prop="lineId">
          <el-input v-model="form.lineId" placeholder="请输入线路ID" />
        </el-form-item>
        <el-form-item label="AGV ID" prop="vehicleId">
          <el-input v-model="form.vehicleId" placeholder="请输入AGV ID" />
        </el-form-item>
        <el-form-item label="升降机ID" prop="elevatorId">
          <el-input v-model="form.elevatorId" placeholder="请输入升降机ID" />
        </el-form-item>
        <el-form-item label="开始时间" prop="startTime">
          <el-date-picker clearable v-model="form.startTime" type="date" value-format="yyyy-MM-dd"
            placeholder="请选择开始时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="结束时间" prop="endTime">
          <el-date-picker clearable v-model="form.endTime" type="date" value-format="yyyy-MM-dd" placeholder="请选择结束时间">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="运行状态" prop="status">
          <el-input v-model="form.status" placeholder="请输入运行状态" />
        </el-form-item>
        <el-form-item label="实际速度" prop="actualSpeed">
          <el-input v-model="form.actualSpeed" placeholder="请输入实际速度" />
        </el-form-item>
        <el-form-item label="运行方向" prop="direction">
          <el-input v-model="form.direction" placeholder="请输入运行方向" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-select v-model="form.delFlag" placeholder="请选择删除标志">
            <el-option v-for="dict in dict.type.del_flag" :key="dict.value" :label="dict.label"
              :value="parseInt(dict.value)"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listOperationLine, getOperationLine, delOperationLine, addOperationLine, updateOperationLine } from "@/api/base/operationLine";

export default {
  name: "OperationLine",
  dicts: ['del_flag'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 运行线路表表格数据
      operationLineList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        operationCode: null,
        lineId: null,
        vehicleId: null,
        elevatorId: null,
        startTime: null,
        endTime: null,
        status: null,
        actualSpeed: null,
        direction: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        operationCode: [
          { required: true, message: "运行编号不能为空", trigger: "blur" }
        ],
        lineId: [
          { required: true, message: "线路ID不能为空", trigger: "blur" }
        ],
        vehicleId: [
          { required: true, message: "AGV ID不能为空", trigger: "blur" }
        ],
        elevatorId: [
          { required: true, message: "升降机ID不能为空", trigger: "blur" }
        ],
        startTime: [
          { required: true, message: "开始时间不能为空", trigger: "blur" }
        ],
        endTime: [
          { required: true, message: "结束时间不能为空", trigger: "blur" }
        ],
        status: [
          { required: true, message: "运行状态不能为空", trigger: "blur" }
        ],
        actualSpeed: [
          { required: true, message: "实际速度不能为空", trigger: "blur" }
        ],
        direction: [
          { required: true, message: "运行方向不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询运行线路表列表 */
    getList() {
      this.loading = true;
      listOperationLine(this.queryParams).then(response => {
        this.operationLineList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        operationId: null,
        operationCode: null,
        lineId: null,
        vehicleId: null,
        elevatorId: null,
        startTime: null,
        endTime: null,
        status: null,
        actualSpeed: null,
        direction: null,
        delFlag: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.operationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加运行线路表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const operationId = row.operationId || this.ids;
      getOperationLine(operationId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改运行线路表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.operationId != null) {
            updateOperationLine(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addOperationLine(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const operationIds = row.operationId || this.ids;
      this.$modal.confirm('是否确认删除@Model.FunctionName)编号为"' + operationIds + '"的数据项？').then(function() {
        return delOperationLine(operationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('base/operationLine/export', {
        ...this.queryParams
      }, `operationLine_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
