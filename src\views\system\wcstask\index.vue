<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="WMS任务ID" prop="wmsTaskId">
        <el-input
          v-model="queryParams.wmsTaskId"
          placeholder="请输入WMS任务ID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="RGV编号" prop="vehicleId">
        <el-input
          v-model="queryParams.vehicleId"
          placeholder="请输入RGV编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务类型" prop="taskType">
        <el-select v-model="queryParams.taskType" placeholder="请选择任务类型" clearable>
          <el-option
            v-for="dict in dict.type.wcs_task_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-input
          v-model="queryParams.priority"
          placeholder="请输入优先级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务状态" prop="taskStatus">
        <el-select v-model="queryParams.taskStatus" placeholder="请选择任务状态" clearable>
          <el-option
            v-for="dict in dict.type.task_sts"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="执行状态" prop="jkSts">
        <el-select v-model="queryParams.jkSts" placeholder="请选择执行状态" clearable>
          <el-option
            v-for="dict in dict.type.jk_sts"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini"
          @click="handleAdd"
          v-hasPermi="['system:task:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:task:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleBatchDelete"
          v-hasPermi="['system:task:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini"
          @click="handleExport"
          v-hasPermi="['system:task:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-check" size="mini" :disabled="single"
          @click="handleBatchFinish"
        >任务全部完成</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-close" size="mini" :disabled="single"
          @click="handleBatchCancel"
        >任务全部取消</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table 
      v-loading="loading" 
      :data="treeData" 
      @selection-change="handleSelectionChange"
      row-key="wcsTaskId"
      :tree-props="{children: 'children'}"
      :row-class-name="tableRowClassName"
      border
      stripe
      highlight-current-row
      :header-cell-style="{background:'#f5f7fa', color:'#606266'}"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="任务类型" align="center" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.nodeType === 'backup'" type="info">备份任务</el-tag>
          <el-tag v-else type="success">主任务</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="WCS任务ID" align="center" prop="wcsTaskId" />
      <el-table-column label="WMS任务ID" align="center" prop="wmsTaskId" />
      <el-table-column label="RGV编号" align="center" prop="vehicleId" />
      <el-table-column label="业务类型" align="center" prop="taskType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.wcs_task_type" :value="scope.row.taskType" />
        </template>
      </el-table-column>
      <el-table-column label="优先级" align="center" prop="priority" />
      <el-table-column label="任务状态" align="center" prop="taskStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.task_sts" :value="scope.row.taskStatus" />
        </template>
      </el-table-column>
      <el-table-column label="错误代码" align="center" prop="errorCode" />
      <el-table-column label="错误信息" align="center" prop="errorMessage" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="起始库位" align="center" prop="sourceLocationId" />
      <el-table-column label="目标库位" align="center" prop="targetLocationId" />
      <el-table-column label="货物信息" align="center" prop="code" />
      <el-table-column label="执行状态" align="center" prop="jkSts">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.jk_sts" :value="scope.row.jkSts" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button v-if="scope.row.nodeType === 'task'" size="mini" type="text" icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:task:edit']"
          >修改</el-button>
          <el-button v-else size="mini" type="text" icon="el-icon-edit"
            @click="handleUpdateBak(scope.row)"
            v-hasPermi="['system:wcsBak:edit']"
          >修改</el-button>
          
          <el-button v-if="scope.row.nodeType === 'task'" size="mini" type="text" icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:task:remove']"
          >删除</el-button>
          <el-button v-else size="mini" type="text" icon="el-icon-delete"
            @click="handleDeleteBak(scope.row)"
            v-hasPermi="['system:wcsBak:remove']"
          >删除</el-button>
          
          <el-button v-if="scope.row.nodeType === 'task'" size="mini" type="text" icon="el-icon-copy-document"
            @click="handleAddBackup(scope.row)"
          >添加备份</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改WCS任务表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="WCS任务ID" prop="wcsTaskId" v-if="form.wcsTaskId">
              <el-input v-model="form.wcsTaskId" placeholder="请输入WCS任务ID" :disabled="true" />
            </el-form-item>
            <el-form-item label="WMS任务ID" prop="wmsTaskId" v-else>
              <el-input v-model="form.wmsTaskId" placeholder="请输入WMS任务ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="RGV编号" prop="vehicleId">
              <el-input v-model="form.vehicleId" placeholder="请输入RGV编号" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="任务类型" prop="taskType">
              <el-select v-model="form.taskType" placeholder="请选择任务类型" style="width: 100%">
                <el-option
                  v-for="dict in dict.type.wcs_task_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-input v-model="form.priority" placeholder="请输入优先级" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="起始库位" prop="sourceLocationId">
              <el-input v-model="form.sourceLocationId" placeholder="请输入起始库位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标库位" prop="targetLocationId">
              <el-input v-model="form.targetLocationId" placeholder="请输入目标库位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="货物信息" prop="code">
              <el-input v-model="form.code" placeholder="请输入货物信息" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="任务状态" prop="taskStatus">
              <el-select v-model="form.taskStatus" placeholder="请选择任务状态">
                <el-option
                  v-for="dict in dict.type.task_sts"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="执行状态" prop="jkSts">
              <el-select v-model="form.jkSts" placeholder="请选择执行状态">
                <el-option
                  v-for="dict in dict.type.jk_sts"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="错误代码" prop="errorCode">
              <el-input v-model="form.errorCode" placeholder="请输入错误代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="错误信息" prop="errorMessage">
              <el-input v-model="form.errorMessage" type="textarea" placeholder="请输入错误信息" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="备注" prop="remark">
              <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12" v-if="form.createTime || title.includes('修改')">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="form.createTime"
                type="datetime"
                placeholder="选择日期时间"
                style="width: 100%"
                value-format="yyyy-MM-dd HH:mm:ss"
                :disabled="title.includes('修改')"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.nodeType === 'backup'">
          <el-col :span="24">
            <div class="el-form-item__label" style="width: 100%; text-align: center; color: #909399; margin-top: 10px;">
              <el-divider content-position="center">备份任务信息</el-divider>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTask, getTask, delTask, addTask, updateTask, forceFinishTask, cancelTask } from "@/api/system/wsctask";
import { listWcsBak, getWcsBak, delWcsBak, addWcsBak, updateWcsBak } from "@/api/system/wcsBak";
import * as echarts from 'echarts';

export default {
  name: "Task",
  dicts: ['wcs_task_type', 'task_sts', 'jk_sts'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      taskIds: [], // 主任务ID
      bakIds: [], // 备份任务ID
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // WCS任务表格数据
      taskList: [],
      // WCS备份任务数据
      wcsBakList: [],
      // 合并后的树形结构数据
      treeData: [],
      // 是否为表格视图
      isTableView: true,
      // 是否显示图表视图
      showChartView: false,
      // 日期范围
      daterangeCreateTime: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        wcsTaskId: null,
        wmsTaskId: null,
        vehicleId: null,
        taskType: null,
        priority: null,
        taskStatus: null,
        errorCode: null,
        errorMessage: null,
        remark: null,
        sourceLocationId: null,
        targetLocationId: null,
        code: null,
        jkSts: null
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        wcsTaskId: [
          { required: true, message: "WCS任务ID不能为空", trigger: "blur" }
        ],
        wmsTaskId: [
          { required: true, message: "WMS任务ID不能为空", trigger: "blur" }
        ],
        vehicleId: [
          { required: true, message: "RGV编号不能为空", trigger: "blur" }
        ],
        taskType: [
          { required: true, message: "任务类型不能为空", trigger: "change" }
        ],
        priority: [
          { required: true, message: "优先级不能为空", trigger: "blur" }
        ],
        taskStatus: [
          { required: true, message: "任务状态不能为空", trigger: "change" }
        ],
        sourceLocationId: [
          { required: true, message: "起始库位不能为空", trigger: "blur" }
        ],
        targetLocationId: [
          { required: true, message: "目标库位不能为空", trigger: "blur" }
        ],
        code: [
          { required: true, message: "货物信息不能为空", trigger: "blur" }
        ],
        jkSts: [
          { required: true, message: "执行状态不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询WCS任务和备份任务，并合并为树形结构 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }

      console.log('开始获取数据...');
      console.log('查询参数:', this.queryParams);

      // 同时请求 wcstask 和 wcsBak 的数据
      Promise.all([
        listTask(this.queryParams).catch(error => {
          console.error("获取 WCS 任务数据失败", error);
          return { rows: [], total: 0 };
        }),
        listWcsBak(this.queryParams).catch(error => {
          console.error("获取 WCS 备份任务数据失败", error);
          return { rows: [], total: 0 };
        })
      ]).then(([taskResponse, bakResponse]) => {
        console.log('WCS任务数据获取成功:', taskResponse);
        console.log('WCS备份任务数据获取成功:', bakResponse);
        
        // 保存原始数据
        this.taskList = taskResponse.rows || [];
        this.wcsBakList = bakResponse.rows || [];
        
        console.log('开始构建树形结构...');
        
        // 合并数据
        this.treeData = this.buildTreeStructure(this.taskList, this.wcsBakList);
        
        // 更新总数
        this.total = this.treeData.length;
        this.loading = false;
        
        console.log('数据处理完成，总记录数:', this.total);
        
        if (this.showChartView) {
          this.$nextTick(() => {
            this.initCharts();
          });
        }
      }).catch(error => {
        console.error("数据处理失败", error);
        this.loading = false;
      });
    },

    /** 构建树形结构 */
    buildTreeStructure(tasks, backups) {
      console.log('原始任务数据:', JSON.stringify(tasks));
      console.log('原始备份数据:', JSON.stringify(backups));
      
      // 创建一个映射表，用于快速查找任务
      const taskMap = {};
      
      // 处理主任务，为每个任务添加类型标识
      const processedTasks = tasks.map(task => {
        // 确保主任务拥有唯一ID并标记类型
        const processedTask = {
          ...task,
          nodeType: 'task',  // 标识为主任务
          children: [],
          // 确保主任务有创建时间
          createTime: task.createTime || new Date().toISOString()
        };
        
        // 使用ID作为key存储到映射表
        taskMap[task.wcsTaskId] = processedTask;
        return processedTask;
      });
      
      // 处理备份任务，并添加对应的父任务ID关联
      if (backups && backups.length > 0) {
        backups.forEach(backup => {
          // 确保所有必要字段都包含
          const backupNode = {
            ...backup,
            nodeType: 'backup',  // 标识为备份任务
            createTime: backup.createTime || new Date().toISOString() // 确保有创建时间
          };
          
          // 查找父任务 - 使用wcsTaskId作为关联字段
          const parentTask = taskMap[backup.wcsTaskId];
          if (parentTask) {
            // 确保children数组存在
            if (!parentTask.children) {
              parentTask.children = [];
            }
            parentTask.children.push(backupNode);
            console.log(`将备份任务 ${backup.wcsTaskId} 添加到父任务下`);
          } else {
            // 如果没有找到对应的父任务，则忽略这个备份任务
            console.log(`备份任务 ${backup.wcsTaskId} 没有找到对应的主任务，已忽略`);
          }
        });
      }
      
      // 清理空的children数组
      processedTasks.forEach(task => {
        if (task.children && task.children.length === 0) {
          delete task.children;
        }
      });
      
      console.log('处理后的树形数据:', JSON.stringify(processedTasks));
      return processedTasks;
    },
    
    /** 根据节点类型设置行的样式 */
    tableRowClassName({row}) {
      if (row.nodeType === 'backup') {
        return 'backup-row';  // 为备份任务行添加特殊样式
      }
      return '';
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {
        wcsTaskId: undefined,
        wmsTaskId: undefined,
        vehicleId: undefined,
        taskType: undefined,
        priority: undefined,
        taskStatus: undefined,
        errorCode: undefined,
        errorMessage: undefined,
        remark: undefined,
        sourceLocationId: undefined,
        targetLocationId: undefined,
        code: undefined,
        jkSts: undefined,
        createTime: undefined,
        nodeType: 'task' // 默认为主任务
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 获取表格中选中的数据 */
    handleSelectionChange(selection) {
      // 分离选中的主任务和备份任务
      const taskIds = selection.filter(item => item.nodeType === 'task').map(item => item.wcsTaskId);
      const bakIds = selection.filter(item => item.nodeType === 'backup').map(item => item.wcsTaskId);
      
      // 所有选中项的ID
      this.ids = selection.map(item => item.wcsTaskId);
      
      // 禁用或启用按钮
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
      
      // 存储分类后的ID，方便后续操作
      this.$set(this, 'taskIds', taskIds);
      this.$set(this, 'bakIds', bakIds);
      
      console.log("选中项:", {
        all: this.ids,
        taskIds: this.taskIds,
        bakIds: this.bakIds
      });
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加WCS任务表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const wcsTaskId = row.wcsTaskId || this.ids[0];

      // 尝试调用后端API获取详情
      getTask(wcsTaskId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改WCS任务表";
      }).catch(error => {
        console.log("后端API调用失败，使用模拟数据", error);

        // 使用模拟数据作为后备方案
        setTimeout(() => {
          const item = this.mockData.find(item => item.wcsTaskId === wcsTaskId);
          if (item) {
            this.form = JSON.parse(JSON.stringify(item));
            this.open = true;
            this.title = "修改WCS任务表";
          }
        }, 300);
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const isBackup = this.form.nodeType === 'backup';
          if (this.form.wcsTaskId != null) {
            // 修改操作
            if (isBackup) {
              // 修改备份任务
              updateWcsBak(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }).catch(error => {
                console.log("后端API调用失败", error);
                this.$modal.msgError("修改失败");
              });
            } else {
              // 修改主任务
              updateTask(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }).catch(error => {
                console.log("后端API调用失败", error);
                this.$modal.msgError("修改失败");
              });
            }
          } else {
            // 新增操作
            if (isBackup) {
              // 新增备份任务
              addWcsBak(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }).catch(error => {
                console.log("后端API调用失败", error);
                this.$modal.msgError("新增失败");
              });
            } else {
              // 新增主任务
              addTask(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }).catch(error => {
                console.log("后端API调用失败", error);
                this.$modal.msgError("新增失败");
              });
            }
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const wcsTaskIds = row.wcsTaskId || this.ids;
      this.$modal.confirm('是否确认删除WCS任务编号为"' + wcsTaskIds + '"的数据项？').then(function() {
        // 尝试调用后端API删除
        return delTask(wcsTaskIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(error => {
        // 判断是否是用户取消操作
        if (error === 'cancel') {
          return;
        }

        console.log("后端API调用失败，使用模拟数据", error);

        // 使用模拟数据作为后备方案
        setTimeout(() => {
          if (Array.isArray(wcsTaskIds)) {
            this.mockData = this.mockData.filter(item => !wcsTaskIds.includes(item.wcsTaskId));
          } else {
            this.mockData = this.mockData.filter(item => item.wcsTaskId !== wcsTaskIds);
          }
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }, 300);
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      try {
        this.download('system/task/export', {
          ...this.queryParams
        }, `task_${new Date().getTime()}.xlsx`);
      } catch (error) {
        console.log("导出功能调用失败，显示模拟消息", error);
        this.$modal.msgSuccess("模拟导出功能：已导出到文件 task_" + new Date().getTime() + ".xlsx");
      }
    },
    /** 单任务完成 */
    handleBatchFinish() {
      if (this.single) {
        this.$modal.msgWarning('请先勾选一条任务');
        return;
      }
      const id = this.ids[0];
      this.$modal.confirm('确定要将该任务完成吗？').then(() => {
        return forceFinishTask({id:id});
      }).then(res => {
        this.$modal.msgSuccess('操作成功');
        this.getList();
      }).catch(err => {
        if (err === 'cancel') return;
        this.$modal.msgError('操作失败');
      });
    },
    /** 单任务取消 */
    handleBatchCancel() {
      if (this.single) {
        this.$modal.msgWarning('请先勾选一条任务');
        return;
      }
      const id = this.ids[0];
      this.$modal.confirm('确定要将该任务取消吗？').then(() => {
        return cancelTask({id:id});
      }).then(res => {
        this.$modal.msgSuccess('操作成功');
        this.getList();
      }).catch(err => {
        if (err === 'cancel') return;
        this.$modal.msgError('操作失败');
      });
    },
    /** 批量删除按钮操作 */
    handleBatchDelete() {
      if (!this.ids || this.ids.length === 0) {
        this.$modal.msgWarning("请先选择要删除的数据");
        return;
      }
      
      const taskIds = this.taskIds || [];
      const bakIds = this.bakIds || [];
      
      // 显示确认对话框
      let confirmMessage = "";
      if (taskIds.length > 0 && bakIds.length > 0) {
        confirmMessage = `是否确认删除主任务编号为"${taskIds.join(',')}"和备份任务编号为"${bakIds.join(',')}"的数据项？`;
      } else if (taskIds.length > 0) {
        confirmMessage = `是否确认删除主任务编号为"${taskIds.join(',')}"的数据项？`;
      } else if (bakIds.length > 0) {
        confirmMessage = `是否确认删除备份任务编号为"${bakIds.join(',')}"的数据项？`;
      }
      
      this.$modal.confirm(confirmMessage).then(() => {
        // 创建一个promises数组，用于存储所有删除请求
        const deletePromises = [];
        
        // 处理主任务删除
        if (taskIds.length > 0) {
          deletePromises.push(delTask(taskIds).catch(error => {
            console.error("删除主任务失败:", error);
            return Promise.reject("主任务删除失败");
          }));
        }
        
        // 处理备份任务删除
        if (bakIds.length > 0) {
          deletePromises.push(delWcsBak(bakIds).catch(error => {
            console.error("删除备份任务失败:", error);
            return Promise.reject("备份任务删除失败");
          }));
        }
        
        // 等待所有删除操作完成
        return Promise.all(deletePromises);
      }).then(() => {
        // 删除成功，刷新数据
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(error => {
        // 判断是否是用户取消操作
        if (error === 'cancel') {
          return;
        }
        console.error("删除操作失败:", error);
        this.$modal.msgError(typeof error === 'string' ? error : "删除失败");
      });
    },
    
    /** 修改备份任务按钮操作 */
    handleUpdateBak(row) {
      this.reset();
      const wcsTaskId = row.wcsTaskId || this.ids[0];
      getWcsBak(wcsTaskId).then(response => {
        this.form = response.data;
        this.form.nodeType = 'backup'; // 标记为备份任务
        // 如果没有创建时间，则设置为当前时间
        if (!this.form.createTime) {
          this.form.createTime = new Date().toLocaleString();
        }
        this.open = true;
        this.title = "修改备份任务";
      }).catch(error => {
        console.log("后端API调用失败", error);
        // 使用行数据作为备份
        setTimeout(() => {
          this.form = JSON.parse(JSON.stringify(row));
          this.form.nodeType = 'backup'; // 标记为备份任务
          // 如果没有创建时间，则设置为当前时间
          if (!this.form.createTime) {
            this.form.createTime = new Date().toLocaleString();
          }
          this.open = true;
          this.title = "修改备份任务";
        }, 300);
      });
    },

    /** 删除备份任务按钮操作 */
    handleDeleteBak(row) {
      const wcsTaskIds = row.wcsTaskId || this.ids;
      this.$modal.confirm('是否确认删除备份任务编号为"' + wcsTaskIds + '"的数据项？').then(function() {
        return delWcsBak(wcsTaskIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(error => {
        // 判断是否是用户取消操作
        if (error === 'cancel') {
          return;
        }
        console.log("后端API调用失败", error);
        this.$modal.msgError("删除失败");
      });
    },

    /** 添加备份任务 */
    handleAddBackup(row) {
      this.reset();
      this.form = {
        wcsTaskId: row.wcsTaskId, // 使用相同的wcsTaskId作为关联
        wmsTaskId: row.wmsTaskId,
        vehicleId: row.vehicleId,
        taskType: row.taskType,
        priority: row.priority,
        taskStatus: '0', // 默认状态为待处理
        sourceLocationId: row.sourceLocationId,
        targetLocationId: row.targetLocationId,
        code: row.code,
        jkSts: '0', // 默认执行状态
        errorCode: '',
        errorMessage: '',
        remark: '备份任务 - 原任务ID: ' + row.wcsTaskId,
        nodeType: 'backup', // 标记为备份任务
        createTime: new Date().toLocaleString() // 设置当前时间
      };
      this.open = true;
      this.title = "添加备份任务";
    },
  }
};
</script>

<style scoped>
/* 备份任务行的样式 */
/deep/ .backup-row {
  background-color: #f8f8ff !important;
  color: #666;
  font-style: italic;
}

/* 树形节点缩进和样式 */
/deep/ .el-table__row--level-1 {
  background-color: rgba(245, 247, 250, 0.5) !important;
}

/* 树形结构展开图标样式 */
/deep/ .el-table__expand-icon {
  color: #409EFF;
  font-size: 16px;
}

/deep/ .el-table__expand-icon--expanded {
  transform: rotate(90deg);
}

/* 为备份任务增加缩进，强调层级关系 */
/deep/ .el-table__row--level-1 td:first-child {
  padding-left: 20px;
}

/* 表格样式优化 */
.el-table {
  margin-top: 10px;
}

/* 表格内部单元格样式 */
.el-table td {
  padding: 8px 0;
}

/* 表头样式 */
.el-table th {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: bold;
  padding: 8px 0;
}
</style>
