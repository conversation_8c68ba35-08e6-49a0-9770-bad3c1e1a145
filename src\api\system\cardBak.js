import request from '@/utils/request'

// 查询路线历史任务列表
export function listCardBak(query) {
  return request({
    url: 'task/subWcsTaskBak/list',
    method: 'get',
    params: query
  })
}

// 查询路线历史任务详细
export function getCardBak(subwcsTaskId) {
  return request({
    url: 'task/subWcsTaskBak/' + subwcsTaskId,
    method: 'get'
  })
}

// 新增路线历史任务
export function addCardBak(data) {
  return request({
    url: 'task/subWcsTaskBak',
    method: 'post',
    data: data
  })
}

// 修改路线历史任务
export function updateCardBak(data) {
  return request({
    url: 'task/subWcsTaskBak',
    method: 'put',
    data: data
  })
}

// 删除路线历史任务
export function delCardBak(subwcsTaskId) {
  return request({
    url: 'task/subWcsTaskBak' + subwcsTaskId,
    method: 'delete'
  })
}
