<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="库位编号" prop="locationCode">
        <el-input
          v-model="queryParams.locationCode"
          placeholder="请输入库位编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="WMS库位编号" prop="wmsLocationCode" label-width="120px">
        <el-input
          v-model="queryParams.wmsLocationCode" 
          placeholder="请输入WMS库位编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="库位类型" prop="locationType">
        <el-select v-model="queryParams.locationType" placeholder="请选择库位类型" clearable>
          <el-option
            v-for="dict in dict.type.location_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="区域编号" prop="areaCode">
        <el-input
          v-model="queryParams.areaCode"
          placeholder="请输入区域编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="X坐标" prop="xCoordinate">
        <el-input
          v-model="queryParams.xCoordinate"
          placeholder="请输入X坐标"
          style="width: 125px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="Y坐标" prop="yCoordinate">
        <el-input
          v-model="queryParams.yCoordinate"
          placeholder="请输入Y坐标"
          style="width: 125px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="Z坐标" prop="zCoordinate">
        <el-input
          v-model="queryParams.zCoordinate"
          placeholder="请输入Z坐标"
          style="width: 125px"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      
      <el-form-item label="WMS库位状态" prop="wmsLocationStatus"  label-width="120px">
        <el-select v-model="queryParams.wmsLocationStatus" placeholder="请选择WMS库位状态" clearable>
          <el-option
            v-for="dict in dict.type.wms_location_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="库位状态" prop="locationStatus">
        <el-select v-model="queryParams.locationStatus" placeholder="请选择库位状态" clearable>
          <el-option
            v-for="dict in dict.type.wcs_location_status"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="WMS货位标识" prop="wmsLocationFlag"  label-width="120px">
        <el-select v-model="queryParams.wmsLocationFlag" placeholder="请选择WMS货位标识" clearable>
          <el-option
            v-for="dict in dict.type.wms_location_flag"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini"
          @click="handleAdd"
          v-hasPermi="['base:location:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['base:location:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['base:location:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini"
          @click="handleExport"
          v-hasPermi="['base:location:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="locationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="库位ID" align="center" prop="locationId" />
      <el-table-column label="库位编号" align="center" prop="locationCode" />
      <el-table-column label="WMS库位ID" align="center" prop="wmsLocationId" />
      <el-table-column label="WMS库位编号" align="center" prop="wmsLocationCode" />
      <el-table-column label="库位名称" align="center" prop="locationName" />
      <el-table-column label="库位类型" align="center" prop="locationType">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.location_type" :value="scope.row.locationType" />
        </template>
      </el-table-column>
      <el-table-column label="区域ID" align="center" prop="areaId" />
      <el-table-column label="区域编号" align="center" prop="areaCode" />
      <el-table-column label="区域名称" align="center" prop="areaName" />
      <el-table-column label="X坐标" align="center" prop="xCoordinate" />
      <el-table-column label="Y坐标" align="center" prop="yCoordinate" />
      <el-table-column label="Z坐标" align="center" prop="zCoordinate" />
      <el-table-column label="二维码内容" align="center" prop="qrCode" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="WMS库位状态" align="center" prop="wmsLocationStatus">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.wms_location_status" :value="scope.row.wmsLocationStatus" />
        </template>
      </el-table-column>
      <el-table-column label="WMS货位标识" align="center" prop="wmsLocationFlag">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.wms_location_flag" :value="scope.row.wmsLocationFlag" />
        </template>
      </el-table-column>
      <el-table-column label="库位状态" align="center" prop="locationStatus">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.wcs_location_status" :value="scope.row.locationStatus" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['base:location:edit']"
          >修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['base:location:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改库位信息对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="库位编号" prop="locationCode">
          <el-input v-model="form.locationCode" placeholder="请输入库位编号" />
        </el-form-item>
        <el-form-item label="库位名称" prop="locationName">
          <el-input v-model="form.locationName" placeholder="请输入库位名称" />
        </el-form-item>
        <el-form-item label="WMS库位ID" prop="wmsLocationId">
          <el-input v-model="form.wmsLocationId" placeholder="请输入WMS库位ID" />
        </el-form-item>
        <el-form-item label="WMS库位编号" prop="wmsLocationCode">
          <el-input v-model="form.wmsLocationCode" placeholder="请输入WMS库位编号" />
        </el-form-item>
        <el-form-item label="库位类型 1=母轨； 2=子轨；3=非库位；" prop="locationType">
          <el-select v-model="form.locationType" placeholder="请选择库位类型 1=母轨； 2=子轨；3=非库位；">
            <el-option
              v-for="dict in dict.type.location_type"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="区域ID" prop="areaId">
          <el-input v-model="form.areaId" placeholder="请输入区域ID" />
        </el-form-item>
        <el-form-item label="区域编号" prop="areaCode">
          <el-input v-model="form.areaCode" placeholder="请输入区域编号" />
        </el-form-item>
        <el-form-item label="区域名称" prop="areaName">
          <el-input v-model="form.areaName" placeholder="请输入区域名称" />
        </el-form-item>
        <el-form-item label="X坐标" prop="xCoordinate">
          <el-input v-model="form.xCoordinate" placeholder="请输入X坐标" />
        </el-form-item>
        <el-form-item label="Y坐标" prop="yCoordinate">
          <el-input v-model="form.yCoordinate" placeholder="请输入Y坐标" />
        </el-form-item>
        <el-form-item label="Z坐标" prop="zCoordinate">
          <el-input v-model="form.zCoordinate" placeholder="请输入Z坐标" />
        </el-form-item>
        <el-form-item label="二维码内容" prop="qrCode">
          <el-input v-model="form.qrCode" placeholder="请输入二维码内容" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" placeholder="请输入备注" />
        </el-form-item>
        <el-form-item label="WMS库位状态" prop="wmsLocationStatus">
          <el-select v-model="form.wmsLocationStatus" placeholder="请选择WMS库位状态">
            <el-option
              v-for="dict in dict.type.wms_location_status"
              :key="dict.value"
              :label="dict.label"
              :value = "parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="WMS货位标识" prop="wmsLocationFlag">
          <el-select v-model="form.wmsLocationFlag" placeholder="请选择WMS货位标识">
            <el-option
              v-for="dict in dict.type.wms_location_flag"
              :key="dict.value"
              :label="dict.label"
              :value = "parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="库位状态" prop="locationStatus">
          <el-select v-model="form.locationStatus" placeholder="请选择库位状态">
            <el-option
              v-for="dict in dict.type.wcs_location_status"
              :key="dict.value"
              :label="dict.label"
              :value = "parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listLocation, getLocation, delLocation, addLocation, updateLocation } from "@/api/base/location";

export default {
  name: "Location",
  dicts: ['location_type', 'wms_location_status', 'wms_location_flag', 'wcs_location_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 库位信息表格数据
      locationList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        locationCode: null,
        locationName: null,
        locationType: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        xCoordinate: null,
        yCoordinate: null,
        zCoordinate: null,
        qrCode: null,
        wmsLocationId: null,
        wmsLocationCode: null,
        wmsLocationStatus: null,
        wmsLocationFlag: null,
        locationStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        locationCode: [
          { required: true, message: "库位编号不能为空", trigger: "blur" }
        ],
        locationName: [
          { required: true, message: "库位名称不能为空", trigger: "blur" }
        ],
        locationType: [
          { required: true, message: "库位类型 1=母轨； 2=子轨；3=非库位；不能为空", trigger: "change" }
        ],
        areaId: [
          { required: true, message: "区域ID不能为空", trigger: "blur" }
        ],
        areaCode: [
          { required: true, message: "区域编号不能为空", trigger: "blur" }
        ],
        areaName: [
          { required: true, message: "区域名称不能为空", trigger: "blur" }
        ],
        xCoordinate: [
          { required: true, message: "X坐标不能为空", trigger: "blur" }
        ],
        yCoordinate: [
          { required: true, message: "Y坐标不能为空", trigger: "blur" }
        ],
        zCoordinate: [
          { required: true, message: "Z坐标不能为空", trigger: "blur" }
        ],
        qrCode: [
          { required: true, message: "二维码内容不能为空", trigger: "blur" }
        ],
        wmsLocationId: [
          { required: true, message: "WMS库位ID不能为空", trigger: "blur" }
        ],
        wmsLocationCode: [
          { required: true, message: "WMS库位编号不能为空", trigger: "blur" }
        ],
        wmsLocationStatus: [
          { required: true, message: "WMS库位状态", trigger: "change" }
        ],
        wmsLocationFlag: [
          { required: true, message: "WMS货位标识", trigger: "change" }
        ],
        locationStatus: [
          { required: true, message: "库位状态", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询库位信息列表 */
    getList() {
      this.loading = true;
      listLocation(this.queryParams).then(response => {
        this.locationList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        locationId: null,
        locationCode: null,
        locationName: null,
        locationType: null,
        areaId: null,
        areaCode: null,
        areaName: null,
        xCoordinate: null,
        yCoordinate: null,
        zCoordinate: null,
        qrCode: null,
        delFlag: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null,
        wmsLocationId: null,
        wmsLocationCode: null,
        wmsLocationStatus: null,
        wmsLocationFlag: null,
        locationStatus: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.locationId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加库位信息";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const locationId = row.locationId || this.ids;
      getLocation(locationId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改库位信息";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.locationId != null) {
            updateLocation(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addLocation(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const locationIds = row.locationId || this.ids;
      this.$modal.confirm('是否确认删除@Model.FunctionName)编号为"' + locationIds + '"的数据项？').then(function() {
        return delLocation(locationIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('base/location/export', {
        ...this.queryParams
      }, `库位信息_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
