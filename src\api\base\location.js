import request from '@/utils/request'

// 查询库位表列表
export function listLocation(query) {
  return request({
    url: '/base/location/list',
    method: 'get',
    params: query
  })
}

// 查询库位表详细
export function getLocation(locationId) {
  return request({
    url: '/base/location/' + locationId,
    method: 'get'
  })
}

// 新增库位表
export function addLocation(data) {
  return request({
    url: '/base/location',
    method: 'post',
    data: data
  })
}

// 修改库位表
export function updateLocation(data) {
  return request({
    url: '/base/location',
    method: 'put',
    data: data
  })
}

// 删除库位表
export function delLocation(locationId) {
  return request({
    url: '/base/location/' + locationId,
    method: 'delete'
  })
}
