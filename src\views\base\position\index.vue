<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="位置编号" prop="positionCode">
        <el-input
          v-model="queryParams.positionCode"
          placeholder="请输入位置编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="位置名称" prop="positionName">
        <el-input
          v-model="queryParams.positionName"
          placeholder="请输入位置名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="位置类型" prop="positionType">
        <el-select v-model="queryParams.positionType" placeholder="请选择位置类型" clearable>
          <el-option
            v-for="dict in dict.type.position_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="X坐标" prop="xCoordinate">
        <el-input
          v-model="queryParams.xCoordinate"
          placeholder="请输入X坐标"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="Y坐标" prop="yCoordinate">
        <el-input
          v-model="queryParams.yCoordinate"
          placeholder="请输入Y坐标"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="Z坐标" prop="zCoordinate">
        <el-input
          v-model="queryParams.zCoordinate"
          placeholder="请输入Z坐标"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="二维码内容" prop="qrCode">
        <el-input
          v-model="queryParams.qrCode"
          placeholder="请输入二维码内容"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini"
          @click="handleAdd"
          v-hasPermi="['base:position:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['base:position:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['base:position:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini"
          @click="handleExport"
          v-hasPermi="['base:position:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="positionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="位置ID" align="center" prop="positionId" />
      <el-table-column label="位置编号" align="center" prop="positionCode" />
      <el-table-column label="位置名称" align="center" prop="positionName" />
      <el-table-column label="位置类型" align="center" prop="positionType">
        <template slot-scope="scope">
                                <dict-tag :options="dict.type.position_type" :value="scope.row.positionType" />
        </template>
      </el-table-column>
      <el-table-column label="X坐标" align="center" prop="xCoordinate" />
      <el-table-column label="Y坐标" align="center" prop="yCoordinate" />
      <el-table-column label="Z坐标" align="center" prop="zCoordinate" />
      <el-table-column label="二维码内容" align="center" prop="qrCode" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['base:position:edit']"
          >修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['base:position:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改二维码表对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="位置编号" prop="positionCode">
          <el-input v-model="form.positionCode" placeholder="请输入位置编号" />
        </el-form-item>
        <el-form-item label="位置名称" prop="positionName">
          <el-input v-model="form.positionName" placeholder="请输入位置名称" />
        </el-form-item>
        <el-form-item label="位置类型" prop="positionType">
          <el-select v-model="form.positionType" placeholder="请选择位置类型">
            <el-option
              v-for="dict in dict.type.position_type"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="X坐标" prop="xCoordinate">
          <el-input v-model="form.xCoordinate" placeholder="请输入X坐标" />
        </el-form-item>
        <el-form-item label="Y坐标" prop="yCoordinate">
          <el-input v-model="form.yCoordinate" placeholder="请输入Y坐标" />
        </el-form-item>
        <el-form-item label="Z坐标" prop="zCoordinate">
          <el-input v-model="form.zCoordinate" placeholder="请输入Z坐标" />
        </el-form-item>
        <el-form-item label="二维码内容" prop="qrCode">
          <el-input v-model="form.qrCode" placeholder="请输入二维码内容" />
        </el-form-item>
        <el-form-item label="删除标志" prop="delFlag">
          <el-select v-model="form.delFlag" placeholder="请选择删除标志">
            <el-option
              v-for="dict in dict.type.del_flag"
              :key="dict.value"
              :label="dict.label"
              :value = "parseInt(dict.value)"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listPosition, getPosition, delPosition, addPosition, updatePosition } from "@/api/base/position";

export default {
  name: "Position",
  dicts: ['position_type', 'del_flag'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 二维码表表格数据
      positionList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        positionCode: null,
        positionName: null,
        positionType: null,
        xCoordinate: null,
        yCoordinate: null,
        zCoordinate: null,
        qrCode: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        positionCode: [
          { required: true, message: "位置编号不能为空", trigger: "blur" }
        ],
        positionName: [
          { required: true, message: "位置名称不能为空", trigger: "blur" }
        ],
        positionType: [
          { required: true, message: "位置类型不能为空", trigger: "change" }
        ],
        xCoordinate: [
          { required: true, message: "X坐标不能为空", trigger: "blur" }
        ],
        yCoordinate: [
          { required: true, message: "Y坐标不能为空", trigger: "blur" }
        ],
        zCoordinate: [
          { required: true, message: "Z坐标不能为空", trigger: "blur" }
        ],
        qrCode: [
          { required: true, message: "二维码内容不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询二维码表列表 */
    getList() {
      this.loading = true;
      listPosition(this.queryParams).then(response => {
        this.positionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        positionId: null,
        positionCode: null,
        positionName: null,
        positionType: null,
        xCoordinate: null,
        yCoordinate: null,
        zCoordinate: null,
        qrCode: null,
        delFlag: null,
        remark: null,
        createBy: null,
        createTime: null,
        updateBy: null,
        updateTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.positionId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加二维码表";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const positionId = row.positionId || this.ids;
      getPosition(positionId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改二维码表";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.positionId != null) {
            updatePosition(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addPosition(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const positionIds = row.positionId || this.ids;
      this.$modal.confirm('是否确认删除@Model.FunctionName)编号为"' + positionIds + '"的数据项？').then(function() {
        return delPosition(positionIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('base/position/export', {
        ...this.queryParams
      }, `position_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
