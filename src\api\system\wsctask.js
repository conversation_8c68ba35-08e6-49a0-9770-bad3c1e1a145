import request from '@/utils/request'

// 查询WCS任务表列表
export function listTask(query) {
  return request({
    url: 'task/wcsTask/list',
    method: 'get',
    params: query
  })
}

// 查询WCS任务表详细
export function getTask(wcsTaskId) {
  return request({
    url: 'task/wcsTask/' + wcsTaskId,
    method: 'get'
  })
}

// 新增WCS任务表
export function addTask(data) {
  return request({
    url: 'task/wcsTask',
    method: 'post',
    data: data
  })
}

// 修改WCS任务表
export function updateTask(data) {
  return request({
    url: 'task/wcsTask',
    method: 'put',
    data: data
  })
}

// 删除WCS任务表
export function delTask(wcsTaskId) {
  return request({
    url: 'task/wcsTask/' + wcsTaskId,
    method: 'delete'
  })
}

// 强制完成任务
export function forceFinishTask(data) {
  return request({
    url: 'api/WCSTask/TaskForceFinish',
    method: 'post',
    data: data
  })
}

// 取消任务
export function cancelTask(data) {
  return request({
    url: 'api/WCSTask/TaskCancel',
    method: 'post',
    data: data
  })
}
