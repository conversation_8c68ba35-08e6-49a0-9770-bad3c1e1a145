import request from '@/utils/request'

// 获取路由
export const getKg = (query) => {
  return request({
    url: '/base/location/4DLocList',
    method: 'get',
    params: query
  })
}

// 获取车的位置
export const getCC = () => {
  return request({
    url: '/base/vehicle/read',
    method: 'get',
  })
}

// 操作库位状态
export const adminStatus = (data) => {
  return request({
    url: '/base/location/editLocationAttribute',
    method: 'post',
    data
  })
}

export const getClist = () => {
  return request({
    url: '/base/vehicle/dvc',
    method: 'get',
  })
}

export const getTsjlist = () => {
  return request({
    url: '/base/elevator/tsj',
    method: 'get',
  })
}

// 获取报警信息
export function getError() {
  return request({
    url: '/vehicle/error/list',
    method: 'get'
  })
}

// 复位车辆
export function resetVehicle(data) {
  return request({
    url: '/vehicle/reset',
    method: 'post',
    data: data
  })
}

// 启用车辆
export function enableVehicle(data) {
  return request({
    url: '/vehicle/enable',
    method: 'post',
    data: data
  })
}

// 停用车辆
export function disableVehicle(data) {
  return request({
    url: '/vehicle/disable',
    method: 'post',
    data: data
  })
}

// 处理报警
export function processError(data) {
  return request({
    url: '/vehicle/error/process',
    method: 'post',
    data: data
  })
}


// 控制车辆方向
export function controlVehicleDirection(data) {
  return request({
    url: '/vehicle/controlDirection',
    method: 'post',
    data: data
  })
}
