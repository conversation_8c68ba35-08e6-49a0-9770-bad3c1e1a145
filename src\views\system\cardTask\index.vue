<template>
  <div class="app-container">
    <!-- 搜索表单 -->
    <el-card class="search-card" shadow="hover">
      <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="85px">
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="执行过程" prop="wcsTaskProcess">
              <el-select v-model="queryParams.wcsTaskProcess" placeholder="请选择执行过程" clearable style="width: 100%">
                <el-option
                  v-for="dict in dict.type.wcs_task_process"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="WMSID" prop="wmsTaskId">
              <el-input
                v-model="queryParams.wmsTaskId"
                placeholder="请输入WMS任务ID"
                clearable
                @keyup.enter.native="handleQuery"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="WCSID" prop="wcsTaskId">
              <el-input
                v-model="queryParams.wcsTaskId"
                placeholder="请输入WCS任务ID"
                clearable
                @keyup.enter.native="handleQuery"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="AGVID" prop="vehicleId">
              <el-input
                v-model="queryParams.vehicleId"
                placeholder="请输入AGV ID"
                clearable
                @keyup.enter.native="handleQuery"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="任务类型" prop="taskType">
              <el-select v-model="queryParams.taskType" placeholder="请选择任务类型" clearable style="width: 100%">
                <el-option
                  v-for="dict in dict.type.card_task_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="WMS类型" prop="wmsTaskType">
              <el-select v-model="queryParams.wmsTaskType" placeholder="请选择WMS类型" clearable style="width: 100%">
                <el-option
                  v-for="dict in dict.type.wms_task_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="优先级" prop="priority">
              <el-input
                v-model="queryParams.priority"
                placeholder="请输入优先级"
                clearable
                @keyup.enter.native="handleQuery"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="状态" prop="taskStatus">
              <el-select v-model="queryParams.taskStatus" placeholder="请选择状态" clearable style="width: 100%">
                <el-option
                  v-for="dict in dict.type.card_sts"
                  :key="dict.value"
                  :label="dict.label"
                  :value="dict.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="6">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="daterangeCreateTime"
                style="width: 100%"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="18" class="text-right">
            <el-form-item>
              <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
              <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>

    <!-- 操作按钮 -->
    <el-card class="action-card" shadow="hover">
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-plus" size="mini"
            @click="handleAdd"
            v-hasPermi="['system:cardTask:add']"
          >新增主任务</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:cardTask:edit']"
          >修改</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
            @click="handleBatchDelete"
            v-hasPermi="['system:cardTask:remove']"
          >删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="el-icon-download" size="mini"
            @click="handleExport"
            v-hasPermi="['system:cardTask:export']"
          >导出</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>
    </el-card>

    <!-- 表格视图 -->
    <div v-if="isTableView">
      <el-card class="table-card" shadow="hover">
        <el-table
          v-loading="loading"
          :data="treeData"
          @selection-change="handleSelectionChange"
          border
          stripe
          highlight-current-row
          row-key="subwcsTaskId"
          :tree-props="{children: 'children'}"
          :header-cell-style="{background:'#f5f7fa', color:'#606266'}"
          :row-class-name="tableRowClassName"
        >
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="SUBWCS任务ID" align="center" prop="subwcsTaskId" width="120" show-overflow-tooltip />
          <el-table-column label="任务类型" align="center" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.nodeType === 'backup'" type="info">备份任务</el-tag>
              <el-tag v-else type="success">主任务</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="执行过程" align="center" prop="wcsTaskProcess" width="100">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.wcs_task_process" :value="scope.row.wcsTaskProcess" />
            </template>
          </el-table-column>
          <el-table-column label="WMS任务ID" align="center" prop="wmsTaskId" width="120" show-overflow-tooltip />
          <el-table-column label="WCS任务ID" align="center" prop="wcsTaskId" width="120" show-overflow-tooltip />
          <el-table-column label="AGV ID" align="center" prop="vehicleId" width="100" show-overflow-tooltip />
          <el-table-column label="业务类型" align="center" prop="taskType" width="100">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.card_task_type" :value="scope.row.taskType" />
            </template>
          </el-table-column>
          <el-table-column label="WMS类型" align="center" prop="wmsTaskType" width="100">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.wms_task_type" :value="scope.row.wmsTaskType" />
            </template>
          </el-table-column>
          <el-table-column label="托盘号" align="center" prop="code" width="100" show-overflow-tooltip />
          <el-table-column label="起始库位" align="center" prop="sourcePositionId" width="110" show-overflow-tooltip />
          <el-table-column label="目标库位" align="center" prop="targetPositionId" width="110" show-overflow-tooltip />
          <el-table-column label="优先级" align="center" prop="priority" width="80" />
          <el-table-column label="状态" align="center" prop="taskStatus" width="100">
            <template slot-scope="scope">
              <dict-tag :options="dict.type.card_sts" :value="scope.row.taskStatus" />
            </template>
          </el-table-column>
          <el-table-column label="创建时间" align="center" prop="createTime" width="180">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.createTime) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button v-if="scope.row.nodeType === 'task'" size="mini" type="text" icon="el-icon-edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:cardTask:edit']"
              >修改</el-button>
              <el-button v-else size="mini" type="text" icon="el-icon-edit"
                @click="handleUpdateBak(scope.row)"
                v-hasPermi="['system:cardBak:edit']"
              >修改</el-button>
              
              <el-button v-if="scope.row.nodeType === 'task'" size="mini" type="text" icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:cardTask:remove']"
              >删除</el-button>
              <el-button v-else size="mini" type="text" icon="el-icon-delete"
                @click="handleDeleteBak(scope.row)"
                v-hasPermi="['system:cardBak:remove']"
              >删除</el-button>
              
              <el-button v-if="scope.row.nodeType === 'task'" size="mini" type="text" icon="el-icon-copy-document"
                @click="handleAddBackup(scope.row)"
              >添加备份</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-card>
    </div>
    
    <!-- 图表视图 -->
    <div v-else class="chart-container">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>任务类型分布</span>
            </div>
            <div class="chart-wrapper">
              <div ref="taskTypeChart" style="width: 100%; height: 300px"></div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>任务状态分布</span>
            </div>
            <div class="chart-wrapper">
              <div ref="taskStatusChart" style="width: 100%; height: 300px"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="24">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>任务执行趋势</span>
              <div style="float: right; margin-top: -5px;">
                <el-radio-group v-model="trendChartType" size="mini" @change="updateTrendChart">
                  <el-radio-button label="line">折线图</el-radio-button>
                  <el-radio-button label="bar">柱状图</el-radio-button>
                  <el-radio-button label="area">区域图</el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <div class="chart-wrapper">
              <div ref="trendChart" style="width: 100%; height: 300px"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 添加或修改路线任务对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body top="5vh">
      <el-form ref="form" :model="form" :rules="rules" label-width="100px" class="dialog-form">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="执行过程" prop="wcsTaskProcess">
              <el-select v-model="form.wcsTaskProcess" placeholder="请选择执行过程" style="width: 100%">
                <el-option
                  v-for="dict in dict.type.wcs_task_process"
                  :key="dict.value"
                  :label="dict.label"
                  :value = "dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="WMSID" prop="wmsTaskId">
              <el-input v-model="form.wmsTaskId" placeholder="请输入WMS任务ID" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="WCSID" prop="wcsTaskId">
              <el-input v-model="form.wcsTaskId" placeholder="请输入WCS任务ID" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="AGV ID" prop="vehicleId">
              <el-input v-model="form.vehicleId" placeholder="请输入AGV ID" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务类型" prop="taskType">
              <el-select v-model="form.taskType" placeholder="请选择任务类型" style="width: 100%">
                <el-option
                  v-for="dict in dict.type.card_task_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value = "dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="WMS类型" prop="wmsTaskType">
              <el-select v-model="form.wmsTaskType" placeholder="请选择WMS任务类型" style="width: 100%">
                <el-option
                  v-for="dict in dict.type.wms_task_type"
                  :key="dict.value"
                  :label="dict.label"
                  :value = "dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="托盘号" prop="code">
              <el-input v-model="form.code" placeholder="请输入托盘号" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-input v-model="form.priority" placeholder="请输入优先级" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="起始库位" prop="sourcePositionId">
              <el-input v-model="form.sourcePositionId" placeholder="请输入起始位置库位" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标库位" prop="targetPositionId">
              <el-input v-model="form.targetPositionId" placeholder="请输入目标位置库位" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="taskStatus">
              <el-select v-model="form.taskStatus" placeholder="请选择状态" style="width: 100%">
                <el-option
                  v-for="dict in dict.type.card_sts"
                  :key="dict.value"
                  :label="dict.label"
                  :value = "dict.value"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12" v-if="form.nodeType === 'backup' && !form.subwcsTaskId">
            <el-form-item label="父任务ID" prop="parentTaskId">
              <el-input v-model="form.parentTaskId" placeholder="请输入父任务ID" :disabled="title === '添加备份任务'" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12" v-if="form.createTime || title.includes('修改')">
            <el-form-item label="创建时间" prop="createTime">
              <el-date-picker
                v-model="form.createTime"
                type="datetime"
                placeholder="选择日期时间"
                style="width: 100%"
                value-format="yyyy-MM-dd HH:mm:ss"
                :disabled="title.includes('修改')"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row v-if="form.nodeType === 'backup'">
          <el-col :span="24">
            <div class="el-form-item__label" style="width: 100%; text-align: center; color: #909399; margin-top: 10px;">
              <el-divider content-position="center">备份任务信息</el-divider>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCardTask, getCardTask, delCardTask, addCardTask, updateCardTask } from "@/api/system/cardTask";
import { listCardBak, getCardBak, delCardBak, addCardBak, updateCardBak } from "@/api/system/cardBak";
import * as echarts from 'echarts';

export default {
  name: "CardTask",
  dicts: ['wcs_task_process', 'card_task_type', 'wms_task_type', 'card_sts'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 路线任务表格数据
      cardTaskList: [],
      // 路线历史任务数据
      cardBakList: [],
      // 合并后的树形结构数据
      treeData: [],
      // 日期范围
      daterangeCreateTime: [],
      // 是否为表格视图
      isTableView: true,
      // 趋势图表类型
      trendChartType: 'line',
      // 图表实例
      taskTypeChart: null,
      taskStatusChart: null,
      trendChart: null,
      // 模拟数据
      mockData: [
        {
          subwcsTaskId: 1001,
          wcsTaskProcess: "1",
          wmsTaskId: "WMS001",
          wcsTaskId: "WCS001",
          vehicleId: "AGV001",
          taskType: "1",
          wmsTaskType: "1",
          code: "PL001",
          sourcePositionId: "A-01-01",
          targetPositionId: "B-02-03",
          priority: "高",
          taskStatus: "0",
          createTime: "2023-09-15 10:30:00"
        },
        {
          subwcsTaskId: 1002,
          wcsTaskProcess: "2",
          wmsTaskId: "WMS002",
          wcsTaskId: "WCS002",
          vehicleId: "AGV002",
          taskType: "2",
          wmsTaskType: "2",
          code: "PL002",
          sourcePositionId: "C-03-02",
          targetPositionId: "D-04-01",
          priority: "中",
          taskStatus: "1",
          createTime: "2023-09-16 11:20:00"
        },
        {
          subwcsTaskId: 1003,
          wcsTaskProcess: "3",
          wmsTaskId: "WMS003",
          wcsTaskId: "WCS003",
          vehicleId: "AGV003",
          taskType: "3",
          wmsTaskType: "3",
          code: "PL003",
          sourcePositionId: "E-05-03",
          targetPositionId: "F-06-02",
          priority: "低",
          taskStatus: "2",
          createTime: "2023-09-17 14:15:00"
        },
        {
          subwcsTaskId: 1004,
          wcsTaskProcess: "1",
          wmsTaskId: "WMS004",
          wcsTaskId: "WCS004",
          vehicleId: "AGV001",
          taskType: "1",
          wmsTaskType: "1",
          code: "PL004",
          sourcePositionId: "G-07-01",
          targetPositionId: "H-08-02",
          priority: "高",
          taskStatus: "0",
          createTime: "2023-09-18 09:45:00",
          parentTaskId: 1001
        },
        {
          subwcsTaskId: 1005,
          wcsTaskProcess: "2",
          wmsTaskId: "WMS005",
          wcsTaskId: "WCS005",
          vehicleId: "AGV002",
          taskType: "2",
          wmsTaskType: "2",
          code: "PL005",
          sourcePositionId: "I-09-03",
          targetPositionId: "J-10-01",
          priority: "中",
          taskStatus: "1",
          createTime: "2023-09-19 16:30:00",
          parentTaskId: 1002
        },
        {
          subwcsTaskId: 1006,
          wcsTaskProcess: "3",
          wmsTaskId: "WMS006",
          wcsTaskId: "WCS006",
          vehicleId: "AGV003",
          taskType: "3",
          wmsTaskType: "3",
          code: "PL006",
          sourcePositionId: "K-11-02",
          targetPositionId: "L-12-03",
          priority: "高",
          taskStatus: "2",
          createTime: "2023-09-20 13:20:00",
          parentTaskId: 1003
        }
      ],
      // 下一个ID值（用于新增数据）
      nextId: 1009,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        wcsTaskProcess: null,
        wmsTaskId: null,
        wcsTaskId: null,
        vehicleId: null,
        taskType: null,
        wmsTaskType: null,
        code: null,
        sourcePositionId: null,
        targetPositionId: null,
        priority: null,
        taskStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        wcsTaskProcess: [
          { required: true, message: "执行过程不能为空", trigger: "change" }
        ],
        wmsTaskId: [
          { required: true, message: "WMS任务ID不能为空", trigger: "blur" }
        ],
        wcsTaskId: [
          { required: true, message: "WCS任务ID不能为空", trigger: "blur" }
        ],
        vehicleId: [
          { required: true, message: "AGV ID不能为空", trigger: "blur" }
        ],
        taskType: [
          { required: true, message: "任务类型不能为空", trigger: "change" }
        ],
        wmsTaskType: [
          { required: true, message: "WMS任务类型不能为空", trigger: "change" }
        ],
        code: [
          { required: true, message: "托盘号不能为空", trigger: "blur" }
        ],
        sourcePositionId: [
          { required: true, message: "起始位置库位不能为空", trigger: "blur" }
        ],
        targetPositionId: [
          { required: true, message: "目标位置库位不能为空", trigger: "blur" }
        ],
        priority: [
          { required: true, message: "优先级不能为空", trigger: "blur" }
        ],
        taskStatus: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
      },
    };
  },
  created() {
    this.getList();
  },
  mounted() {
    if (!this.isTableView) {
      this.initCharts();
    }
  },
  beforeDestroy() {
    // 销毁图表实例
    this.disposeCharts();
  },
  methods: {
    /** 切换视图 */
    toggleView() {
      this.isTableView = !this.isTableView;
      if (!this.isTableView) {
        this.$nextTick(() => {
          this.initCharts();
        });
      }
    },
    
    /** 销毁图表实例 */
    disposeCharts() {
      if (this.taskTypeChart) {
        this.taskTypeChart.dispose();
        this.taskTypeChart = null;
      }
      if (this.taskStatusChart) {
        this.taskStatusChart.dispose();
        this.taskStatusChart = null;
      }
      if (this.trendChart) {
        this.trendChart.dispose();
        this.trendChart = null;
      }
    },
    
    /** 查询路线任务和历史任务列表，并合并为树形结构 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }

      // 同时请求 cardTask 和 cardBak 的数据
      Promise.all([
        listCardTask(this.queryParams).catch(error => {
          console.log("获取 cardTask 数据失败", error);
          return { rows: this.mockData, total: this.mockData.length };
        }),
        listCardBak(this.queryParams).catch(error => {
          console.log("获取 cardBak 数据失败", error);
          return { rows: [], total: 0 };
        })
      ]).then(([cardTaskResponse, cardBakResponse]) => {
        // 保存原始数据
        this.cardTaskList = cardTaskResponse.rows || [];
        this.cardBakList = cardBakResponse.rows || [];
        
        // 合并数据并构建树形结构
        this.treeData = this.buildTreeStructure(this.cardTaskList, this.cardBakList);
        
        // 更新总数
        this.total = this.treeData.length;
        this.loading = false;
        
        if (!this.isTableView) {
          this.$nextTick(() => {
            this.initCharts();
          });
        }
      }).catch(error => {
        console.log("数据处理失败", error);
        this.loading = false;
      });
    },

    /** 构建树形结构 */
    buildTreeStructure(tasks, backups) {
      // 创建一个映射表，用于快速查找任务
      const taskMap = {};
      
      // 处理主任务，为每个任务添加类型标识
      const processedTasks = tasks.map(task => ({
        ...task,
        nodeType: 'task',  // 标识为主任务
        children: [],
        // 确保主任务也有创建时间，如果没有则添加当前时间
        createTime: task.createTime || new Date().toLocaleString()
      }));
      
      // 将所有主任务添加到映射表
      processedTasks.forEach(task => {
        taskMap[task.subwcsTaskId] = task;
      });
      
      // 处理备份任务，并添加对应的父任务ID关联
      const processedBackups = backups.map(backup => {
        // 确保所有必要字段都包含，包括创建时间
        return {
          ...backup,
          nodeType: 'backup',  // 标识为历史备份任务
          // 如果没有明确的父任务ID，则尝试使用相同的subwcsTaskId作为关联
          parentTaskId: backup.parentTaskId || backup.subwcsTaskId,
          createTime: backup.createTime || new Date().toLocaleString() // 确保有创建时间
        };
      });
      
      // 将备份任务添加到对应的主任务的children中
      processedBackups.forEach(backup => {
        const parentTask = taskMap[backup.parentTaskId];
        if (parentTask) {
          // 如果找到对应的父任务，则添加到其children中
          parentTask.children.push(backup);
        } else {
          // 如果没有找到对应的父任务，则将其视为独立任务
          processedTasks.push({
            ...backup,
            nodeType: 'backup',
            children: []
          });
        }
      });
      
      // 清理空的children数组
      processedTasks.forEach(task => {
        if (task.children && task.children.length === 0) {
          delete task.children;
        }
      });
      
      return processedTasks;
    },
    
    /** 根据节点类型设置行的样式 */
    tableRowClassName({row}) {
      if (row.nodeType === 'backup') {
        return 'backup-row';  // 为备份任务行添加特殊样式
      }
      return '';
    },
    
    /** 初始化图表 */
    initCharts() {
      this.disposeCharts();
      this.$nextTick(() => {
        // 初始化任务类型分布图表
        this.taskTypeChart = echarts.init(this.$refs.taskTypeChart);
        const taskTypeData = this.getTaskTypeData();
        
        const colorPalette = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];
        
        this.taskTypeChart.setOption({
          tooltip: {
            trigger: 'item',
            formatter: '{a} <br/>{b}: {c} ({d}%)'
          },
          legend: {
            orient: 'vertical',
            left: 10,
            top: 'center',
            data: taskTypeData.map(item => item.name)
          },
          series: [
            {
              name: '任务类型',
              type: 'pie',
              radius: ['40%', '70%'],
              avoidLabelOverlap: false,
              itemStyle: {
                borderRadius: 10,
                borderColor: '#fff',
                borderWidth: 2
              },
              label: {
                show: true,
                position: 'outside',
                formatter: '{b}: {c} ({d}%)',
                overflow: 'break',
                width: 80
              },
              labelLine: {
                show: true,
                smooth: 0.2,
                length: 10,
                length2: 20
              },
              emphasis: {
                label: {
                  show: true,
                  fontSize: '16',
                  fontWeight: 'bold'
                }
              },
              data: taskTypeData,
              color: colorPalette
            }
          ],
          animation: true
        });

        // 初始化任务状态分布图表
        this.taskStatusChart = echarts.init(this.$refs.taskStatusChart);
        const taskStatusData = this.getTaskStatusData();
        
        this.taskStatusChart.setOption({
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow'
            }
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: taskStatusData.map(item => item.name),
            axisLabel: {
              interval: 0,
              rotate: 30
            }
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '任务数量',
              type: 'bar',
              data: taskStatusData.map(item => ({
                value: item.value,
                itemStyle: {
                  color: item.color
                }
              })),
              showBackground: true,
              backgroundStyle: {
                color: 'rgba(180, 180, 180, 0.2)'
              },
              barWidth: '40%',
              borderRadius: 5,
              label: {
                show: true,
                position: 'top',
                formatter: '{c}',
                color: '#333'
              }
            }
          ],
          animation: true
        });

        // 初始化任务执行趋势图表
        this.trendChart = echarts.init(this.$refs.trendChart);
        this.updateTrendChart();
      });
    },
    
    /** 更新趋势图表 */
    updateTrendChart() {
      if (!this.trendChart) return;
      
      const trendData = this.getTrendData();
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['待处理', '处理中', '已完成']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: this.trendChartType !== 'line',
          data: trendData.dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '待处理',
            type: this.trendChartType === 'area' ? 'line' : this.trendChartType,
            stack: this.trendChartType === 'area' ? '总量' : '',
            areaStyle: this.trendChartType === 'area' ? { opacity: 0.8 } : null,
            emphasis: {
              focus: 'series'
            },
            data: trendData.pending,
            smooth: true,
            showSymbol: this.trendChartType === 'line' || this.trendChartType === 'area',
            symbolSize: 8,
            label: {
              show: this.trendChartType === 'bar',
              position: 'top',
              formatter: '{c}',
              color: '#333'
            },
            lineStyle: {
              width: 3
            },
            itemStyle: {
              color: '#5470c6'
            }
          },
          {
            name: '处理中',
            type: this.trendChartType === 'area' ? 'line' : this.trendChartType,
            stack: this.trendChartType === 'area' ? '总量' : '',
            areaStyle: this.trendChartType === 'area' ? { opacity: 0.8 } : null,
            emphasis: {
              focus: 'series'
            },
            data: trendData.processing,
            smooth: true,
            showSymbol: this.trendChartType === 'line' || this.trendChartType === 'area',
            symbolSize: 8,
            label: {
              show: this.trendChartType === 'bar',
              position: 'top',
              formatter: '{c}',
              color: '#333'
            },
            lineStyle: {
              width: 3
            },
            itemStyle: {
              color: '#91cc75'
            }
          },
          {
            name: '已完成',
            type: this.trendChartType === 'area' ? 'line' : this.trendChartType,
            stack: this.trendChartType === 'area' ? '总量' : '',
            areaStyle: this.trendChartType === 'area' ? { opacity: 0.8 } : null,
            emphasis: {
              focus: 'series'
            },
            data: trendData.completed,
            smooth: true,
            showSymbol: this.trendChartType === 'line' || this.trendChartType === 'area',
            symbolSize: 8,
            label: {
              show: this.trendChartType === 'bar',
              position: 'top',
              formatter: '{c}',
              color: '#333'
            },
            lineStyle: {
              width: 3
            },
            itemStyle: {
              color: '#fac858'
            }
          }
        ],
        animation: true
      };
      
      this.trendChart.setOption(option);
    },
    
    /** 获取任务类型数据 */
    getTaskTypeData() {
      const taskTypeMap = {};
      const dictMap = {};
      
      // 创建字典映射
      if (this.dict.type.card_task_type) {
        this.dict.type.card_task_type.forEach(dict => {
          dictMap[dict.value] = dict.label;
        });
      }
      
      this.cardTaskList.forEach(item => {
        if (item.taskType) {
          const label = dictMap[item.taskType] || `类型${item.taskType}`;
          taskTypeMap[label] = (taskTypeMap[label] || 0) + 1;
        }
      });
      
      return Object.entries(taskTypeMap).map(([name, value]) => ({ name, value }));
    },
    
    /** 获取任务状态数据 */
    getTaskStatusData() {
      const statusMap = {};
      const dictMap = {};
      const colorMap = {
        '0': '#5470c6', // 待处理
        '1': '#91cc75', // 处理中
        '2': '#fac858'  // 已完成
      };
      
      // 创建字典映射
      if (this.dict.type.card_sts) {
        this.dict.type.card_sts.forEach(dict => {
          dictMap[dict.value] = dict.label;
        });
      }
      
      this.cardTaskList.forEach(item => {
        if (item.taskStatus) {
          const label = dictMap[item.taskStatus] || `状态${item.taskStatus}`;
          statusMap[label] = (statusMap[label] || 0) + 1;
        }
      });
      
      return Object.entries(statusMap).map(([name, value], index) => {
        // 根据状态标识获取颜色，否则使用默认颜色
        const statusKey = Object.keys(dictMap).find(key => dictMap[key] === name);
        const color = statusKey && colorMap[statusKey] ? colorMap[statusKey] : `#${Math.floor(Math.random()*16777215).toString(16)}`;
        return { name, value, color };
      });
    },
    
    /** 获取趋势数据 */
    getTrendData() {
      // 提取有createTime的记录
      const recordsWithDate = this.cardTaskList.filter(item => item.createTime);
      
      // 获取日期列表
      const dates = [...new Set(recordsWithDate.map(item => {
        if (item.createTime && item.createTime.includes(' ')) {
          return item.createTime.split(' ')[0];
        }
        return '';
      }))].filter(date => date).sort();
      
      // 初始化数据
      const pending = new Array(dates.length).fill(0);
      const processing = new Array(dates.length).fill(0);
      const completed = new Array(dates.length).fill(0);

      // 处理数据
      recordsWithDate.forEach(item => {
        if (!item.createTime || !item.createTime.includes(' ')) return;
        
        const dateIndex = dates.indexOf(item.createTime.split(' ')[0]);
        if (dateIndex === -1) return;
        
        if (item.taskStatus === '0') {
          pending[dateIndex]++;
        } else if (item.taskStatus === '1') {
          processing[dateIndex]++;
        } else if (item.taskStatus === '2') {
          completed[dateIndex]++;
        }
      });

      return {
        dates: dates.length ? dates : ['暂无数据'],
        pending: pending.length ? pending : [0],
        processing: processing.length ? processing : [0],
        completed: completed.length ? completed : [0]
      };
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        subwcsTaskId: null,
        wcsTaskProcess: null,
        wmsTaskId: null,
        wcsTaskId: null,
        vehicleId: null,
        taskType: null,
        wmsTaskType: null,
        code: null,
        sourcePositionId: null,
        targetPositionId: null,
        priority: null,
        taskStatus: null,
        createTime: null,
        parentTaskId: null,
        nodeType: 'task'  // 默认为主任务
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 获取表格中选中的数据 */
    handleSelectionChange(selection) {
      // 分离选中的主任务和备份任务
      const taskIds = selection.filter(item => item.nodeType === 'task').map(item => item.subwcsTaskId);
      const bakIds = selection.filter(item => item.nodeType === 'backup').map(item => item.subwcsTaskId);
      
      this.ids = selection.map(item => item.subwcsTaskId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
      
      // 存储分类后的ID，方便后续操作
      this.$set(this, 'taskIds', taskIds);
      this.$set(this, 'bakIds', bakIds);
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      // 设置默认创建时间为当前时间
      this.form.createTime = new Date().toLocaleString();
      this.open = true;
      this.title = "添加路线任务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const subwcsTaskId = row.subwcsTaskId || this.ids[0];
      getCardTask(subwcsTaskId).then(response => {
        this.form = response.data;
        this.form.nodeType = 'task'; // 标记为主任务
        // 如果没有创建时间，则设置为当前时间
        if (!this.form.createTime) {
          this.form.createTime = new Date().toLocaleString();
        }
        this.open = true;
        this.title = "修改路线任务";
      }).catch(error => {
        console.log("后端API调用失败，使用模拟数据", error);

        // 使用行数据作为备份
        setTimeout(() => {
          this.form = JSON.parse(JSON.stringify(row));
          this.form.nodeType = 'task'; // 标记为主任务
          // 如果没有创建时间，则设置为当前时间
          if (!this.form.createTime) {
            this.form.createTime = new Date().toLocaleString();
          }
          this.open = true;
          this.title = "修改路线任务";
        }, 300);
      });
    },
    /** 修改备份任务按钮操作 */
    handleUpdateBak(row) {
      this.reset();
      const subwcsTaskId = row.subwcsTaskId || this.ids[0];
      getCardBak(subwcsTaskId).then(response => {
        this.form = response.data;
        this.form.nodeType = 'backup'; // 标记为备份任务
        this.open = true;
        this.title = "修改备份任务";
      }).catch(error => {
        console.log("后端API调用失败，使用模拟数据", error);
        // 使用行数据作为备份
        setTimeout(() => {
          this.form = JSON.parse(JSON.stringify(row));
          this.form.nodeType = 'backup'; // 标记为备份任务
          this.open = true;
          this.title = "修改备份任务";
        }, 300);
      });
    },
    /** 删除备份任务按钮操作 */
    handleDeleteBak(row) {
      const subwcsTaskIds = row.subwcsTaskId || this.ids;
      this.$modal.confirm('是否确认删除备份任务编号为"' + subwcsTaskIds + '"的数据项？').then(function() {
        return delCardBak(subwcsTaskIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(error => {
        // 判断是否是用户取消操作
        if (error === 'cancel') {
          return;
        }
        console.log("后端API调用失败", error);
        this.$modal.msgError("删除失败");
      });
    },
    /** 添加备份任务 */
    handleAddBackup(row) {
      this.reset();
      this.form = {
        parentTaskId: row.subwcsTaskId,
        wcsTaskProcess: row.wcsTaskProcess,
        wmsTaskId: row.wmsTaskId,
        wcsTaskId: null, // 新备份任务需要新的WCSID
        vehicleId: row.vehicleId,
        taskType: row.taskType,
        wmsTaskType: row.wmsTaskType || '',
        code: row.code,
        sourcePositionId: row.sourcePositionId,
        targetPositionId: row.targetPositionId,
        priority: row.priority,
        taskStatus: '0', // 默认状态为待处理
        nodeType: 'backup', // 标记为备份任务
        createTime: new Date().toLocaleString() // 设置当前时间
      };
      this.open = true;
      this.title = "添加备份任务";
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          const isBackup = this.form.nodeType === 'backup';
          if (this.form.subwcsTaskId != null) {
            // 修改操作
            if (isBackup) {
              // 修改备份任务
              updateCardBak(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }).catch(error => {
                console.log("后端API调用失败", error);
                this.$modal.msgError("修改失败");
              });
            } else {
              // 修改主任务
              updateCardTask(this.form).then(response => {
                this.$modal.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }).catch(error => {
                console.log("后端API调用失败", error);
                this.$modal.msgError("修改失败");
              });
            }
          } else {
            // 新增操作
            if (isBackup) {
              // 新增备份任务
              addCardBak(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }).catch(error => {
                console.log("后端API调用失败", error);
                this.$modal.msgError("新增失败");
              });
            } else {
              // 新增主任务
              addCardTask(this.form).then(response => {
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }).catch(error => {
                console.log("后端API调用失败", error);
                this.$modal.msgError("新增失败");
              });
            }
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const subwcsTaskIds = row.subwcsTaskId || this.ids;
      this.$modal.confirm('是否确认删除路线任务编号为"' + subwcsTaskIds + '"的数据项？').then(function() {
        return delCardTask(subwcsTaskIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(error => {
        // 判断是否是用户取消操作
        if (error === 'cancel') {
          return;
        }

        console.log("后端API调用失败，使用模拟数据", error);

        // 使用模拟数据作为后备方案
        setTimeout(() => {
          if (Array.isArray(subwcsTaskIds)) {
            this.mockData = this.mockData.filter(item => !subwcsTaskIds.includes(item.subwcsTaskId));
          } else {
            this.mockData = this.mockData.filter(item => item.subwcsTaskId !== subwcsTaskIds);
          }
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }, 300);
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      try {
        this.download('system/cardTask/export', {
          ...this.queryParams
        }, `cardTask_${new Date().getTime()}.xlsx`);
      } catch (error) {
        console.log("导出功能调用失败，显示模拟消息", error);
        this.$modal.msgSuccess("模拟导出功能：已导出到文件 cardTask_" + new Date().getTime() + ".xlsx");
      }
    },
    /** 批量删除按钮操作 */
    handleBatchDelete() {
      if (!this.ids || this.ids.length === 0) {
        this.$modal.msgWarning("请先选择要删除的数据");
        return;
      }
      
      const taskIds = this.taskIds || [];
      const bakIds = this.bakIds || [];
      
      const taskPromise = taskIds.length > 0 ? 
        this.$modal.confirm('是否确认删除主任务编号为"' + taskIds.join(',') + '"的数据项？')
          .then(() => delCardTask(taskIds)) : 
        Promise.resolve();
      
      const bakPromise = bakIds.length > 0 ? 
        this.$modal.confirm('是否确认删除备份任务编号为"' + bakIds.join(',') + '"的数据项？')
          .then(() => delCardBak(bakIds)) : 
        Promise.resolve();
      
      Promise.all([taskPromise, bakPromise])
        .then(() => {
          this.getList();
          this.$modal.msgSuccess("删除成功");
        })
        .catch(error => {
          if (error === 'cancel') {
            return;
          }
          console.log("批量删除失败", error);
          this.$modal.msgError("删除失败");
        });
    },
  }
};
</script>

<style scoped>
.app-container {
  padding: 15px;
}

.search-card {
  margin-bottom: 15px;
  border-radius: 5px;
}

.action-card {
  margin-bottom: 15px;
  border-radius: 5px;
}

.table-card {
  border-radius: 5px;
}

.text-right {
  text-align: right;
  padding-right: 20px;
}

.dialog-form {
  padding: 10px 15px;
}

/* 悬停效果 */
.el-button {
  transition: all 0.3s;
}

.el-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* 输入框样式统一 */
.el-input, .el-select {
  width: 100%;
}

/* 卡片内间距 */
.el-card__body {
  padding: 15px;
}

/* 表格行高 */
.el-table td, .el-table th {
  padding: 8px 0;
}

.chart-container {
  padding: 20px;
}

.box-card {
  margin-bottom: 20px;
  transition: all 0.3s;
  border-radius: 8px;
  overflow: hidden;
}

.box-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.chart-wrapper {
  padding: 15px;
  height: 300px;
  position: relative;
}

/* 图表标题样式 */
.clearfix {
  padding: 10px 15px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #f0f0f0;
}

.clearfix span {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

/* 图表视图动画 */
.chart-container .el-row {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 备份任务行的样式 */
/deep/ .backup-row {
  background-color: #f8f8ff;
  color: #666;
  font-style: italic;
}

/deep/ .el-table__row--level-1 {
  background-color: rgba(245, 247, 250, 0.5);
}
</style>
