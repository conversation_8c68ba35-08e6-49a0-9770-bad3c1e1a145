import request from '@/utils/request'

// 查询路线任务列表
export function listCardTask(query) {
  return request({
    url: 'task/subWcsTask/list',
    method: 'get',
    params: query
  })
}

// 查询路线任务详细
export function getCardTask(subwcsTaskId) {
  return request({
    url: 'task/subWcsTask/' + subwcsTaskId,
    method: 'get'
  })
}

// 新增路线任务
export function addCardTask(data) {
  return request({
    url: 'task/subWcsTask',
    method: 'post',
    data: data
  })
}

// 修改路线任务
export function updateCardTask(data) {
  return request({
    url: 'task/subWcsTask',
    method: 'put',
    data: data
  })
}

// 删除路线任务
export function delCardTask(subwcsTaskId) {
  return request({
    url: 'task/subWcsTask/' + subwcsTaskId,
    method: 'delete'
  })
}
