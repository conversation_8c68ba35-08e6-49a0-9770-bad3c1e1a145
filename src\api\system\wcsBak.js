import request from '@/utils/request'

// 查询WCS历史列表
export function listWcsBak(query) {
  return request({
    url: 'task/wcsTaskBak/list',
    method: 'get',
    params: query
  })
}

// 查询WCS历史详细
export function getWcsBak(wcsTaskId) {
  return request({
    url: 'task/wcsTaskBak/' + wcsTaskId,
    method: 'get'
  })
}

// 新增WCS历史
export function addWcsBak(data) {
  return request({
    url: 'task/wcsTaskBak',
    method: 'post',
    data: data
  })
}

// 修改WCS历史
export function updateWcsBak(data) {
  return request({
    url: 'task/wcsTaskBak',
    method: 'put',
    data: data
  })
}

// 删除WCS历史
export function delWcsBak(wcsTaskId) {
  return request({
    url: 'task/wcsTaskBak/' + wcsTaskId,
    method: 'delete'
  })
}
