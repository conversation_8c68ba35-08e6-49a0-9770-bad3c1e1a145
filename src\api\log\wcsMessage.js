import request from '@/utils/request'

// 查询WCS通信消息表列表
export function listWcsMessage(query) {
  return request({
    url: '/log/wcsMessage/list',
    method: 'get',
    params: query
  })
}

// 查询WCS通信消息表详细
export function getWcsMessage(msgId) {
  return request({
    url: '/log/wcsMessage/' + msgId,
    method: 'get'
  })
}

// 新增WCS通信消息表
export function addWcsMessage(data) {
  return request({
    url: '/log/wcsMessage',
    method: 'post',
    data: data
  })
}

// 修改WCS通信消息表
export function updateWcsMessage(data) {
  return request({
    url: '/log/wcsMessage',
    method: 'put',
    data: data
  })
}

// 删除WCS通信消息表
export function delWcsMessage(msgId) {
  return request({
    url: '/log/wcsMessage/' + msgId,
    method: 'delete'
  })
}

// 清空WCS通信消息日志
export function cleanWcsMessage() {
  return request({
    url: '/log/wcsMessage/clean',
    method: 'delete'
  })
}