import request from '@/utils/request'

// 查询WMS接口通讯日志表列表
export function listInterfaceLog(query) {
  return request({
    url: '/log/interfaceLog/list',
    method: 'get',
    params: query
  })
}

// 查询WMS接口通讯日志表详细
export function getInterfaceLog(id) {
  return request({
    url: '/log/interfaceLog/' + id,
    method: 'get'
  })
}

// 新增WMS接口通讯日志表
export function addInterfaceLog(data) {
  return request({
    url: '/log/interfaceLog',
    method: 'post',
    data: data
  })
}

// 修改WMS接口通讯日志表
export function updateInterfaceLog(data) {
  return request({
    url: '/log/interfaceLog',
    method: 'put',
    data: data
  })
}

// 删除WMS接口通讯日志表
export function delInterfaceLog(id) {
  return request({
    url: '/log/interfaceLog/' + id,
    method: 'delete'
  })
}


// 清空操作日志
export function cleanInterfaceLog() {
  return request({
    url: '/log/interfaceLog/clean',
    method: 'delete'
  })
}
