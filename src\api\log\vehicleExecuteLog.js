import request from '@/utils/request'

// 查询四向车执行日志表列表
export function listVehicleExecuteLog(query) {
  return request({
    url: '/log/vehicleExecuteLog/list',
    method: 'get',
    params: query
  })
}

// 查询四向车执行日志表详细
export function getVehicleExecuteLog(id) {
  return request({
    url: '/log/vehicleExecuteLog/' + id,
    method: 'get'
  })
}

// 新增四向车执行日志表
export function addVehicleExecuteLog(data) {
  return request({
    url: '/log/vehicleExecuteLog',
    method: 'post',
    data: data
  })
}

// 修改四向车执行日志表
export function updateVehicleExecuteLog(data) {
  return request({
    url: '/log/vehicleExecuteLog',
    method: 'put',
    data: data
  })
}

// 删除四向车执行日志表
export function delVehicleExecuteLog(id) {
  return request({
    url: '/log/vehicleExecuteLog/' + id,
    method: 'delete'
  })
}


// 清空操作日志
export function cleanVehicleExecuteLog() {
  return request({
    url: '/log/vehicleExecuteLog/clean',
    method: 'delete'
  })
}
