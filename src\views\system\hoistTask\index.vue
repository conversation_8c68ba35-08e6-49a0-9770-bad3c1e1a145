<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="物料类型" prop="goodsType">
        <el-select v-model="queryParams.goodsType" placeholder="请选择物料类型" clearable>
          <el-option v-for="dict in dict.type.goods_type" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="任务状态" prop="taskStatus">
        <el-select v-model="queryParams.taskStatus" placeholder="请选择任务状态" clearable>
          <el-option v-for="dict in dict.type.task_status_ts" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="提升机上料方向" prop="sourceDirection">
        <el-select v-model="queryParams.sourceDirection" placeholder="请选择提升机上料方向" clearable>
          <el-option v-for="dict in dict.type.source_direction" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="提升机上料方向" prop="targetDirection">
        <el-select v-model="queryParams.targetDirection" placeholder="请选择提升机上料方向" clearable>
          <el-option v-for="dict in dict.type.source_direction" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker v-model="daterange${ AttrName }" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
          range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAdd"
          v-hasPermi="['system:hoistTask:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:hoistTask:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:hoistTask:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini" @click="handleExport"
          v-hasPermi="['system:hoistTask:export']">导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="hoistTaskList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="wmsID" align="center" prop="wmsTaskId" />
      <el-table-column label="搬运编号" align="center" prop="code" />
      <el-table-column label="物料类型" align="center" prop="goodsType">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.goods_type" :value="scope.row.goodsType" />
        </template>
      </el-table-column>
      <el-table-column label="任务状态" align="center" prop="taskStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.task_status_ts" :value="scope.row.taskStatus" />
        </template>
      </el-table-column>
      <el-table-column label="起点站点" align="center" prop="sourceStation" />
      <el-table-column label="终点站点" align="center" prop="targetStation" />
      <el-table-column label="起点" align="center" prop="sourceLayer" />
      <el-table-column label="目标" align="center" prop="targetLayer" />
      <el-table-column label="提升机上料方向" align="center" prop="sourceDirection">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.source_direction" :value="scope.row.sourceDirection" />
        </template>
      </el-table-column>
      <el-table-column label="提升机上料方向" align="center" prop="targetDirection">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.source_direction" :value="scope.row.targetDirection" />
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button size="mini" type="text" icon="el-icon-edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:hoistTask:edit']">修改</el-button>
          <el-button size="mini" type="text" icon="el-icon-delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:hoistTask:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
      @pagination="getList" />

    <!-- 添加或修改提升机对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="物料类型" prop="goodsType">
          <el-select v-model="form.goodsType" placeholder="请选择物料类型">
            <el-option v-for="dict in dict.type.goods_type" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="任务状态" prop="taskStatus">
          <el-radio-group v-model="form.taskStatus">
            <el-radio v-for="dict in dict.type.task_status_ts" :key="dict.value"
              :label="dict.value">{{ dict.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="提升机上料方向" prop="sourceDirection">
          <el-select v-model="form.sourceDirection" placeholder="请选择提升机上料方向">
            <el-option v-for="dict in dict.type.source_direction" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="提升机上料方向" prop="targetDirection">
          <el-select v-model="form.targetDirection" placeholder="请选择提升机上料方向">
            <el-option v-for="dict in dict.type.source_direction" :key="dict.value" :label="dict.label"
              :value="dict.value"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listHoistTask, getHoistTask, delHoistTask, addHoistTask, updateHoistTask } from "@/api/system/hoistTask";

export default {
  name: "HoistTask",
  dicts: ['goods_type', 'task_status_ts', 'source_direction'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 提升机表格数据
      hoistTaskList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 提升机上料方向时间范围
      daterangecreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        wmsTaskId: null,
        goodsType: null,
        taskStatus: null,
        sourceStation: null,
        targetStation: null,
        sourceLayer: null,
        targetLayer: null,
        sourceDirection: null,
        targetDirection: null,
        createTime: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        wmsTaskId: [
          { required: true, message: "wmsID不能为空", trigger: "blur" }
        ],
        goodsType: [
          { required: true, message: "物料类型不能为空", trigger: "change" }
        ],
        taskStatus: [
          { required: true, message: "任务状态不能为空", trigger: "change" }
        ],
        sourceStation: [
          { required: true, message: "起点站点不能为空", trigger: "blur" }
        ],
        targetStation: [
          { required: true, message: "终点站点不能为空", trigger: "blur" }
        ],
        sourceLayer: [
          { required: true, message: "起点不能为空", trigger: "blur" }
        ],
        targetLayer: [
          { required: true, message: "目标不能为空", trigger: "blur" }
        ],
        sourceDirection: [
          { required: true, message: "提升机上料方向不能为空", trigger: "change" }
        ],
        targetDirection: [
          { required: true, message: "提升机上料方向不能为空", trigger: "change" }
        ],
        createTime: [
          { required: true, message: "创建时间不能为空", trigger: "blur" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询提升机列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangecreateTime && '' != this.daterangecreateTime) {
        this.queryParams.params["begincreateTime"] = this.daterangecreateTime[0];
        this.queryParams.params["endcreateTime"] = this.daterangecreateTime[1];
      }
      listHoistTask(this.queryParams).then(response => {
        this.hoistTaskList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        wmsTaskId: null,
        code: null,
        goodsType: null,
        taskStatus: null,
        sourceStation: null,
        targetStation: null,
        sourceLayer: null,
        targetLayer: null,
        sourceDirection: null,
        targetDirection: null,
        createTime: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangecreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.code)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加提升机";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const code = row.code || this.ids;
      getHoistTask(code).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改提升机";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.code != null) {
            updateHoistTask(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addHoistTask(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const codes = row.code || this.ids;
      this.$modal.confirm('是否确认删除@Model.FunctionName)编号为"' + codes + '"的数据项？').then(function () {
        return delHoistTask(codes);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => { });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/hoistTask/export', {
        ...this.queryParams
      }, `hoistTask_${new Date().getTime()}.xlsx`)
    }
  }
};
</script>
