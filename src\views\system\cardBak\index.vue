<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="任务过程" prop="wcsTaskProcess">
        <el-input
          v-model="queryParams.wcsTaskProcess"
          placeholder="请输入任务过程"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="AGVID" prop="vehicleId">
        <el-input
          v-model="queryParams.vehicleId"
          placeholder="请输入AGVID"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="任务类型" prop="taskType">
        <el-select v-model="queryParams.taskType" placeholder="请选择任务类型" clearable>
          <el-option
            v-for="dict in dict.type.card_task_type"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="优先级" prop="priority">
        <el-input
          v-model="queryParams.priority"
          placeholder="请输入优先级"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="taskStatus">
        <el-select v-model="queryParams.taskStatus" placeholder="请选择状态" clearable>
          <el-option
            v-for="dict in dict.type.card_sts"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间">
        <el-date-picker
          v-model="daterangeCreateTime"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="el-icon-plus" size="mini"
          @click="handleAdd"
          v-hasPermi="['system:cardBak:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="el-icon-edit" size="mini" :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:cardBak:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="el-icon-delete" size="mini" :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:cardBak:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="el-icon-download" size="mini"
          @click="handleExport"
          v-hasPermi="['system:cardBak:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="info" plain icon="el-icon-data-analysis" size="mini"
          @click="toggleView"
        >{{ isTableView ? '图表视图' : '表格视图' }}</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <!-- 表格视图 -->
    <div v-if="isTableView">
      <el-table v-loading="loading" :data="cardBakList" row-key="subwcsTaskId" :tree-props="{children: 'children'}" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="SUBWCS任务ID" align="center" prop="subwcsTaskId" />
        <el-table-column label="任务过程" align="center" prop="wcsTaskProcess">
          <template slot-scope="scope">
                                <dict-tag :options="dict.type.wcs_task_process" :value="scope.row.wcsTaskProcess" />
          </template>
        </el-table-column>
        <el-table-column label="WMSID" align="center" prop="wmsTaskId" />
        <el-table-column label="AGVID" align="center" prop="vehicleId" />
        <el-table-column label="任务类型" align="center" prop="taskType">
          <template slot-scope="scope">
                                <dict-tag :options="dict.type.card_task_type" :value="scope.row.taskType" />
          </template>
        </el-table-column>
        <el-table-column label="起始位置" align="center" prop="sourcePositionId" />
        <el-table-column label="目标位置" align="center" prop="targetPositionId" />
        <el-table-column label="优先级" align="center" prop="priority" />
        <el-table-column label="状态" align="center" prop="taskStatus">
          <template slot-scope="scope">
                                <dict-tag :options="dict.type.card_sts" :value="scope.row.taskStatus" />
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime" width="180">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button size="mini" type="text" icon="el-icon-edit"
              @click="handleUpdate(scope.row)"
              v-hasPermi="['system:cardBak:edit']"
            >修改</el-button>
            <el-button size="mini" type="text" icon="el-icon-delete"
              @click="handleDelete(scope.row)"
              v-hasPermi="['system:cardBak:remove']"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </div>

    <!-- 图表视图 -->
    <div v-else class="chart-container">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>任务类型分布</span>
            </div>
            <div class="chart-wrapper">
              <div ref="taskTypeChart" style="width: 100%; height: 300px"></div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>任务状态分布</span>
            </div>
            <div class="chart-wrapper">
              <div ref="taskStatusChart" style="width: 100%; height: 300px"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <el-row :gutter="20" style="margin-top: 20px">
        <el-col :span="24">
          <el-card class="box-card" shadow="hover">
            <div slot="header" class="clearfix">
              <span>任务执行趋势</span>
              <div style="float: right; margin-top: -5px;">
                <el-radio-group v-model="trendChartType" size="mini" @change="updateTrendChart">
                  <el-radio-button label="line">折线图</el-radio-button>
                  <el-radio-button label="bar">柱状图</el-radio-button>
                  <el-radio-button label="area">区域图</el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <div class="chart-wrapper">
              <div ref="trendChart" style="width: 100%; height: 300px"></div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 添加或修改路线历史任务对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="任务过程" prop="wcsTaskProcess">
          <el-input v-model="form.wcsTaskProcess" placeholder="请输入任务过程" />
        </el-form-item>
        <el-form-item label="AGVID" prop="vehicleId">
          <el-input v-model="form.vehicleId" placeholder="请输入AGVID" />
        </el-form-item>
        <el-form-item label="任务类型" prop="taskType">
          <el-select v-model="form.taskType" placeholder="请选择任务类型">
            <el-option
              v-for="dict in dict.type.card_task_type"
              :key="dict.value"
              :label="dict.label"
              :value = "dict.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="优先级" prop="priority">
          <el-input v-model="form.priority" placeholder="请输入优先级" />
        </el-form-item>
        <el-form-item label="状态" prop="taskStatus">
          <el-radio-group v-model="form.taskStatus">
            <el-radio
              v-for="dict in dict.type.card_sts"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="父任务ID" prop="parentTaskId" v-if="!form.subwcsTaskId">
          <el-input v-model="form.parentTaskId" placeholder="请输入父任务ID（可选）" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCardBak, getCardBak, delCardBak, addCardBak, updateCardBak } from "@/api/system/cardBak";
import * as echarts from 'echarts';

export default {
  name: "CardBak",
  dicts: ['card_task_type', 'card_sts', 'wcs_task_process'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 路线历史任务表格数据
      cardBakList: [],
      // 是否为表格视图
      isTableView: true,
      // 趋势图表类型
      trendChartType: 'line',
      // 图表实例
      taskTypeChart: null,
      taskStatusChart: null,
      trendChart: null,
      // 模拟数据
      mockData: [
        {
          subwcsTaskId: 1001,
          wcsTaskProcess: "1",
          wmsTaskId: "WMS001",
          vehicleId: "AGV001",
          taskType: "1",
          sourcePositionId: "A-01-01",
          targetPositionId: "B-02-03",
          priority: "高",
          taskStatus: "0",
          createTime: "2023-09-15 10:30:00"
        },
        {
          subwcsTaskId: 1002,
          wcsTaskProcess: "2",
          wmsTaskId: "WMS002",
          vehicleId: "AGV002",
          taskType: "2",
          sourcePositionId: "C-03-02",
          targetPositionId: "D-04-01",
          priority: "中",
          taskStatus: "1",
          createTime: "2023-09-16 11:20:00"
        },
        {
          subwcsTaskId: 1003,
          wcsTaskProcess: "3",
          wmsTaskId: "WMS003",
          vehicleId: "AGV003",
          taskType: "3",
          sourcePositionId: "E-05-03",
          targetPositionId: "F-06-02",
          priority: "低",
          taskStatus: "2",
          createTime: "2023-09-17 14:15:00"
        },
        {
          subwcsTaskId: 1004,
          wcsTaskProcess: "1",
          wmsTaskId: "WMS004",
          vehicleId: "AGV001",
          taskType: "1",
          sourcePositionId: "G-07-01",
          targetPositionId: "H-08-02",
          priority: "高",
          taskStatus: "0",
          createTime: "2023-09-18 09:45:00",
          parentTaskId: 1001
        },
        {
          subwcsTaskId: 1005,
          wcsTaskProcess: "2",
          wmsTaskId: "WMS005",
          vehicleId: "AGV002",
          taskType: "2",
          sourcePositionId: "I-09-03",
          targetPositionId: "J-10-01",
          priority: "中",
          taskStatus: "1",
          createTime: "2023-09-19 16:30:00",
          parentTaskId: 1002
        },
        {
          subwcsTaskId: 1006,
          wcsTaskProcess: "3",
          wmsTaskId: "WMS006",
          vehicleId: "AGV003",
          taskType: "3",
          sourcePositionId: "K-11-02",
          targetPositionId: "L-12-03",
          priority: "高",
          taskStatus: "2",
          createTime: "2023-09-20 13:20:00",
          parentTaskId: 1003
        }
      ],
      // 下一个ID值（用于新增数据）
      nextId: 1007,
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 日期范围
      daterangeCreateTime: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        wcsTaskProcess: null,
        wmsTaskId: null,
        vehicleId: null,
        taskType: null,
        sourcePositionId: null,
        targetPositionId: null,
        priority: null,
        taskStatus: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        wcsTaskProcess: [
          { required: true, message: "任务过程不能为空", trigger: "blur" }
        ],
        wmsTaskId: [
          { required: true, message: "WMSID不能为空", trigger: "blur" }
        ],
        vehicleId: [
          { required: true, message: "AGVID不能为空", trigger: "blur" }
        ],
        taskType: [
          { required: true, message: "任务类型不能为空", trigger: "change" }
        ],
        sourcePositionId: [
          { required: true, message: "起始位置不能为空", trigger: "blur" }
        ],
        targetPositionId: [
          { required: true, message: "目标位置不能为空", trigger: "blur" }
        ],
        priority: [
          { required: true, message: "优先级不能为空", trigger: "blur" }
        ],
        taskStatus: [
          { required: true, message: "状态不能为空", trigger: "change" }
        ],
      }
    };
  },
  created() {
    this.getList();
  },
  mounted() {
    if (!this.isTableView) {
      this.initCharts();
    }
  },
  beforeDestroy() {
    // 销毁图表实例
    if (this.taskTypeChart) {
      this.taskTypeChart.dispose();
    }
    if (this.taskStatusChart) {
      this.taskStatusChart.dispose();
    }
    if (this.trendChart) {
      this.trendChart.dispose();
    }
  },
  methods: {
    /** 切换视图 */
    toggleView() {
      this.isTableView = !this.isTableView;
      if (!this.isTableView) {
        this.$nextTick(() => {
          this.initCharts();
        });
      }
    },
    /** 初始化图表 */
    initCharts() {
      // 初始化任务类型分布图表
      this.taskTypeChart = echarts.init(this.$refs.taskTypeChart);
      const taskTypeData = this.getTaskTypeData();
      
      const colorPalette = ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4'];
      
      this.taskTypeChart.setOption({
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 10,
          top: 'center',
          data: taskTypeData.map(item => item.name)
        },
        series: [
          {
            name: '任务类型',
            type: 'pie',
            radius: ['40%', '70%'],
            avoidLabelOverlap: false,
            itemStyle: {
              borderRadius: 10,
              borderColor: '#fff',
              borderWidth: 2
            },
            label: {
              show: false,
              position: 'center'
            },
            emphasis: {
              label: {
                show: true,
                fontSize: '18',
                fontWeight: 'bold'
              }
            },
            labelLine: {
              show: false
            },
            data: taskTypeData,
            color: colorPalette
          }
        ]
      });

      // 初始化任务状态分布图表
      this.taskStatusChart = echarts.init(this.$refs.taskStatusChart);
      const taskStatusData = this.getTaskStatusData();
      
      this.taskStatusChart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: taskStatusData.map(item => item.name),
          axisLabel: {
            interval: 0,
            rotate: 30
          }
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '任务数量',
            type: 'bar',
            data: taskStatusData.map(item => ({
              value: item.value,
              itemStyle: {
                color: item.color
              }
            })),
            showBackground: true,
            backgroundStyle: {
              color: 'rgba(180, 180, 180, 0.2)'
            },
            barWidth: '40%',
            borderRadius: 5
          }
        ]
      });

      // 初始化任务执行趋势图表
      this.trendChart = echarts.init(this.$refs.trendChart);
      this.updateTrendChart();
    },
    
    /** 更新趋势图表 */
    updateTrendChart() {
      if (!this.trendChart) return;
      
      const trendData = this.getTrendData();
      
      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985'
            }
          }
        },
        legend: {
          data: ['待处理', '处理中', '已完成']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: this.trendChartType !== 'line',
          data: trendData.dates
        },
        yAxis: {
          type: 'value'
        },
        series: [
          {
            name: '待处理',
            type: this.trendChartType,
            stack: this.trendChartType === 'area' ? '总量' : '',
            areaStyle: this.trendChartType === 'area' ? { opacity: 0.8 } : null,
            emphasis: {
              focus: 'series'
            },
            data: trendData.pending,
            smooth: true,
            lineStyle: {
              width: 3
            },
            itemStyle: {
              color: '#5470c6'
            }
          },
          {
            name: '处理中',
            type: this.trendChartType,
            stack: this.trendChartType === 'area' ? '总量' : '',
            areaStyle: this.trendChartType === 'area' ? { opacity: 0.8 } : null,
            emphasis: {
              focus: 'series'
            },
            data: trendData.processing,
            smooth: true,
            lineStyle: {
              width: 3
            },
            itemStyle: {
              color: '#91cc75'
            }
          },
          {
            name: '已完成',
            type: this.trendChartType,
            stack: this.trendChartType === 'area' ? '总量' : '',
            areaStyle: this.trendChartType === 'area' ? { opacity: 0.8 } : null,
            emphasis: {
              focus: 'series'
            },
            data: trendData.completed,
            smooth: true,
            lineStyle: {
              width: 3
            },
            itemStyle: {
              color: '#fac858'
            }
          }
        ]
      };
      
      this.trendChart.setOption(option);
    },
    
    /** 获取任务类型数据 */
    getTaskTypeData() {
      const taskTypeMap = {};
      const dictMap = {};
      
      // 创建字典映射
      if (this.dict.type.card_task_type) {
        this.dict.type.card_task_type.forEach(dict => {
          dictMap[dict.value] = dict.label;
        });
      }
      
      this.cardBakList.forEach(item => {
        if (item.taskType) {
          const label = dictMap[item.taskType] || `类型${item.taskType}`;
          taskTypeMap[label] = (taskTypeMap[label] || 0) + 1;
        }
      });
      
      return Object.entries(taskTypeMap).map(([name, value]) => ({ name, value }));
    },
    
    /** 获取任务状态数据 */
    getTaskStatusData() {
      const statusMap = {};
      const dictMap = {};
      const colorMap = {
        '0': '#5470c6', // 待处理
        '1': '#91cc75', // 处理中
        '2': '#fac858'  // 已完成
      };
      
      // 创建字典映射
      if (this.dict.type.card_sts) {
        this.dict.type.card_sts.forEach(dict => {
          dictMap[dict.value] = dict.label;
        });
      }
      
      this.cardBakList.forEach(item => {
        if (item.taskStatus) {
          const label = dictMap[item.taskStatus] || `状态${item.taskStatus}`;
          statusMap[label] = (statusMap[label] || 0) + 1;
        }
      });
      
      return Object.entries(statusMap).map(([name, value], index) => {
        // 根据状态标识获取颜色，否则使用默认颜色
        const statusKey = Object.keys(dictMap).find(key => dictMap[key] === name);
        const color = statusKey && colorMap[statusKey] ? colorMap[statusKey] : `#${Math.floor(Math.random()*16777215).toString(16)}`;
        return { name, value, color };
      });
    },
    
    /** 获取趋势数据 */
    getTrendData() {
      // 提取有createTime的记录
      const recordsWithDate = this.cardBakList.filter(item => item.createTime);
      
      // 获取日期列表
      const dates = [...new Set(recordsWithDate.map(item => {
        if (item.createTime && item.createTime.includes(' ')) {
          return item.createTime.split(' ')[0];
        }
        return '';
      }))].filter(date => date).sort();
      
      // 初始化数据
      const pending = new Array(dates.length).fill(0);
      const processing = new Array(dates.length).fill(0);
      const completed = new Array(dates.length).fill(0);

      // 处理数据
      recordsWithDate.forEach(item => {
        if (!item.createTime || !item.createTime.includes(' ')) return;
        
        const dateIndex = dates.indexOf(item.createTime.split(' ')[0]);
        if (dateIndex === -1) return;
        
        if (item.taskStatus === '0') {
          pending[dateIndex]++;
        } else if (item.taskStatus === '1') {
          processing[dateIndex]++;
        } else if (item.taskStatus === '2') {
          completed[dateIndex]++;
        }
      });

      return {
        dates: dates.length ? dates : ['暂无数据'],
        pending: pending.length ? pending : [0],
        processing: processing.length ? processing : [0],
        completed: completed.length ? completed : [0]
      };
    },
    
    /** 将平面数据转换为树形结构 */
    convertToTreeData(data) {
      // 创建一个映射表，用于快速查找任务
      const taskMap = {};
      // 创建结果数组
      const result = [];
      
      // 首先，将所有任务添加到映射表中
      data.forEach(task => {
        // 确保每个任务都有一个children数组
        task.children = [];
        taskMap[task.subwcsTaskId] = task;
      });
      
      // 然后，构建树形结构
      data.forEach(task => {
        // 如果任务有父任务ID
        if (task.parentTaskId && taskMap[task.parentTaskId]) {
          // 将当前任务添加到父任务的children数组中
          taskMap[task.parentTaskId].children.push(task);
        } else {
          // 如果没有父任务ID，则为顶级任务
          result.push(task);
        }
      });
      
      // 清理空的children数组
      const cleanEmptyChildren = (nodes) => {
        nodes.forEach(node => {
          if (node.children && node.children.length === 0) {
            delete node.children;
          } else if (node.children && node.children.length > 0) {
            cleanEmptyChildren(node.children);
          }
        });
      };
      
      cleanEmptyChildren(result);
      return result;
    },
    
    /** 查询路线历史任务列表 */
    getList() {
      this.loading = true;
      this.queryParams.params = {};
      if (null != this.daterangeCreateTime && '' != this.daterangeCreateTime) {
        this.queryParams.params["beginCreateTime"] = this.daterangeCreateTime[0];
        this.queryParams.params["endCreateTime"] = this.daterangeCreateTime[1];
      }
      listCardBak(this.queryParams).then(response => {
        // 将平面数据转换为树形结构
        this.cardBakList = this.convertToTreeData(response.rows);
        this.total = response.total;
        this.loading = false;
        
        if (!this.isTableView) {
          this.$nextTick(() => {
            this.initCharts();
          });
        }
      }).catch(error => {
        console.log("后端API调用失败，使用模拟数据", error);

        // 使用模拟数据作为后备方案
        setTimeout(() => {
          // 过滤模拟数据
          let filteredList = [...this.mockData];

          // 根据查询条件过滤
          if (this.queryParams.wcsTaskProcess) {
            filteredList = filteredList.filter(item =>
              item.wcsTaskProcess === this.queryParams.wcsTaskProcess
            );
          }

          if (this.queryParams.wmsTaskId) {
            filteredList = filteredList.filter(item =>
              item.wmsTaskId && item.wmsTaskId.includes(this.queryParams.wmsTaskId)
            );
          }

          if (this.queryParams.vehicleId) {
            filteredList = filteredList.filter(item =>
              item.vehicleId && item.vehicleId.includes(this.queryParams.vehicleId)
            );
          }

          if (this.queryParams.taskType) {
            filteredList = filteredList.filter(item =>
              item.taskType === this.queryParams.taskType
            );
          }

          if (this.queryParams.taskStatus) {
            filteredList = filteredList.filter(item =>
              item.taskStatus === this.queryParams.taskStatus
            );
          }

          if (this.queryParams.sourcePositionId) {
            filteredList = filteredList.filter(item =>
              item.sourcePositionId && item.sourcePositionId.includes(this.queryParams.sourcePositionId)
            );
          }

          if (this.queryParams.targetPositionId) {
            filteredList = filteredList.filter(item =>
              item.targetPositionId && item.targetPositionId.includes(this.queryParams.targetPositionId)
            );
          }

          if (this.queryParams.priority) {
            filteredList = filteredList.filter(item =>
              item.priority && item.priority.includes(this.queryParams.priority)
            );
          }

          // 日期范围过滤
          if (this.daterangeCreateTime && this.daterangeCreateTime.length === 2) {
            const beginTime = new Date(this.daterangeCreateTime[0]).getTime();
            const endTime = new Date(this.daterangeCreateTime[1]).getTime();
            
            filteredList = filteredList.filter(item => {
              const itemTime = new Date(item.createTime).getTime();
              return itemTime >= beginTime && itemTime <= endTime;
            });
          }

          // 将平面数据转换为树形结构
          const treeData = this.convertToTreeData(filteredList);
          
          // 计算分页
          const start = (this.queryParams.pageNum - 1) * this.queryParams.pageSize;
          const end = start + this.queryParams.pageSize;

          this.cardBakList = treeData.slice(start, end);
          this.total = treeData.length;
          this.loading = false;
          
          if (!this.isTableView) {
            this.$nextTick(() => {
              this.initCharts();
            });
          }
        }, 500);
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        subwcsTaskId: null,
        wcsTaskProcess: null,
        wmsTaskId: null,
        vehicleId: null,
        taskType: null,
        sourcePositionId: null,
        targetPositionId: null,
        priority: null,
        taskStatus: null,
        createTime: null,
        parentTaskId: null
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.daterangeCreateTime = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.subwcsTaskId)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加路线历史任务";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const subwcsTaskId = row.subwcsTaskId || this.ids[0];
      getCardBak(subwcsTaskId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改路线历史任务";
      }).catch(error => {
        console.log("后端API调用失败，使用模拟数据", error);

        // 使用模拟数据作为后备方案
        setTimeout(() => {
          const item = this.mockData.find(item => item.subwcsTaskId === subwcsTaskId);
          if (item) {
            this.form = JSON.parse(JSON.stringify(item));
            this.open = true;
            this.title = "修改路线历史任务";
          }
        }, 300);
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.subwcsTaskId != null) {
            updateCardBak(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.log("后端API调用失败，使用模拟数据", error);

              // 使用模拟数据作为后备方案
              setTimeout(() => {
                const index = this.mockData.findIndex(item => item.subwcsTaskId === this.form.subwcsTaskId);
                if (index !== -1) {
                  this.mockData[index] = JSON.parse(JSON.stringify(this.form));
                  this.$modal.msgSuccess("修改成功");
                  this.open = false;
                  this.getList();
                }
              }, 300);
            });
          } else {
            addCardBak(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            }).catch(error => {
              console.log("后端API调用失败，使用模拟数据", error);

              // 使用模拟数据作为后备方案
              setTimeout(() => {
                const newItem = JSON.parse(JSON.stringify(this.form));
                newItem.subwcsTaskId = this.nextId++;
                newItem.createTime = new Date().toLocaleString();
                this.mockData.unshift(newItem);
                this.$modal.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }, 300);
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const subwcsTaskIds = row.subwcsTaskId || this.ids;
      this.$modal.confirm('是否确认删除路线历史任务编号为"' + subwcsTaskIds + '"的数据项？').then(function() {
        return delCardBak(subwcsTaskIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(error => {
        // 判断是否是用户取消操作
        if (error === 'cancel') {
          return;
        }

        console.log("后端API调用失败，使用模拟数据", error);

        // 使用模拟数据作为后备方案
        setTimeout(() => {
          if (Array.isArray(subwcsTaskIds)) {
            this.mockData = this.mockData.filter(item => !subwcsTaskIds.includes(item.subwcsTaskId));
          } else {
            this.mockData = this.mockData.filter(item => item.subwcsTaskId !== subwcsTaskIds);
          }
          this.getList();
          this.$modal.msgSuccess("删除成功");
        }, 300);
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      try {
        this.download('system/cardBak/export', {
          ...this.queryParams
        }, `cardBak_${new Date().getTime()}.xlsx`);
      } catch (error) {
        console.log("导出功能调用失败，显示模拟消息", error);
        this.$modal.msgSuccess("模拟导出功能：已导出到文件 cardBak_" + new Date().getTime() + ".xlsx");
      }
    }
  }
};
</script>

<style scoped>
.chart-container {
  padding: 20px;
}
.chart-wrapper {
  padding: 20px;
  height: 300px;
}
.box-card {
  margin-bottom: 20px;
  transition: all 0.3s;
}
.box-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}
</style>
