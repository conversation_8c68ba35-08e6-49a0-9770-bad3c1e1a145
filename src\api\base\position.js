import request from '@/utils/request'

// 查询二维码表列表
export function listPosition(query) {
  return request({
    url: '/base/position/list',
    method: 'get',
    params: query
  })
}

// 查询二维码表详细
export function getPosition(positionId) {
  return request({
    url: '/base/position/' + positionId,
    method: 'get'
  })
}

// 新增二维码表
export function addPosition(data) {
  return request({
    url: '/base/position',
    method: 'post',
    data: data
  })
}

// 修改二维码表
export function updatePosition(data) {
  return request({
    url: '/base/position',
    method: 'put',
    data: data
  })
}

// 删除二维码表
export function delPosition(positionId) {
  return request({
    url: '/base/position/' + positionId,
    method: 'delete'
  })
}
