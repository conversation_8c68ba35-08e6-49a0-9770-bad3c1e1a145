import request from '@/utils/request'

// 查询WCS历史列表
export function listWcsBak(query) {
  return request({
    url: '/system/wcsBak/list',
    method: 'get',
    params: query
  })
}

// 查询WCS历史详细
export function getWcsBak(wcsTaskId) {
  return request({
    url: '/system/wcsBak/' + wcsTaskId,
    method: 'get'
  })
}

// 新增WCS历史
export function addWcsBak(data) {
  return request({
    url: '/system/wcsBak',
    method: 'post',
    data: data
  })
}

// 修改WCS历史
export function updateWcsBak(data) {
  return request({
    url: '/system/wcsBak',
    method: 'put',
    data: data
  })
}

// 删除WCS历史
export function delWcsBak(wcsTaskId) {
  return request({
    url: '/system/wcsBak/' + wcsTaskId,
    method: 'delete'
  })
}
