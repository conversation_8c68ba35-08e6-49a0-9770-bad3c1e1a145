import request from '@/utils/request'

// 查询运行线路表列表
export function listOperationLine(query) {
  return request({
    url: '/base/operationLine/list',
    method: 'get',
    params: query
  })
}

// 查询运行线路表详细
export function getOperationLine(operationId) {
  return request({
    url: '/base/operationLine/' + operationId,
    method: 'get'
  })
}

// 新增运行线路表
export function addOperationLine(data) {
  return request({
    url: '/base/operationLine',
    method: 'post',
    data: data
  })
}

// 修改运行线路表
export function updateOperationLine(data) {
  return request({
    url: '/base/operationLine',
    method: 'put',
    data: data
  })
}

// 删除运行线路表
export function delOperationLine(operationId) {
  return request({
    url: '/base/operationLine/' + operationId,
    method: 'delete'
  })
}
